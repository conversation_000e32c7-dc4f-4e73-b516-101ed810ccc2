/// Model class representing a product category
class Category {
  /// Unique identifier for the category
  int? id;

  /// Name of the category
  String name;

  /// Description of the category
  String? description;

  /// Constructor for creating a Category instance
  Category({
    this.id,
    required this.name,
    this.description,
  });

  /// Converts the Category instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
    };
  }

  /// Creates a Category instance from a Map (typically from database)
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'] as int?,
      name: map['name'] as String? ?? '',
      description: map['description'] as String?,
    );
  }

  @override
  String toString() {
    return 'Category{id: $id, name: $name, description: $description}';
  }
}
