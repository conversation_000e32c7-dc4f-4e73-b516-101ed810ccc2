// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'أسامة ماركت';

  @override
  String get appSubtitle => 'إدارة ذكية لمتجرك';

  @override
  String get dashboard => 'الشاشة الرئيسية';

  @override
  String get sales => 'المبيعات';

  @override
  String get purchases => 'المشتريات';

  @override
  String get products => 'المنتجات';

  @override
  String get customers => 'العملاء';

  @override
  String get suppliers => 'الموردين';

  @override
  String get inventory => 'المخزون';

  @override
  String get reports => 'التقارير';

  @override
  String get settings => 'الإعدادات';

  @override
  String get backup => 'النسخ الاحتياطي';

  @override
  String get add => 'إضافة';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get ok => 'موافق';

  @override
  String get close => 'إغلاق';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get refresh => 'تحديث';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومة';

  @override
  String get name => 'الاسم';

  @override
  String get description => 'الوصف';

  @override
  String get price => 'السعر';

  @override
  String get quantity => 'الكمية';

  @override
  String get total => 'الإجمالي';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get phone => 'الهاتف';

  @override
  String get address => 'العنوان';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get notes => 'ملاحظات';

  @override
  String get currency => 'ر.س';

  @override
  String get welcomeMessage => 'مرحباً بك في أسامة ماركت';

  @override
  String get noDataFound => 'لا توجد بيانات';

  @override
  String get confirmDelete => 'هل أنت متأكد من الحذف؟';

  @override
  String get operationSuccessful => 'تمت العملية بنجاح';

  @override
  String get operationFailed => 'فشلت العملية';

  @override
  String get permissionRequired => 'الصلاحية مطلوبة';

  @override
  String get permissionDenied => 'تم رفض الصلاحية';

  @override
  String get storagePermission => 'صلاحية التخزين';

  @override
  String get cameraPermission => 'صلاحية الكاميرا';

  @override
  String get locationPermission => 'صلاحية الموقع';

  @override
  String get contactsPermission => 'صلاحية جهات الاتصال';
}
