import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/analytics_provider.dart';
import '../../providers/sale_provider.dart';
import '../../providers/expense_provider.dart';
import '../../providers/internal_transfer_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/store_inventory_provider.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAnalyticsData();
    });
  }

  Future<void> _loadAnalyticsData() async {
    final AnalyticsProvider analyticsProvider =
        context.read<AnalyticsProvider>();
    final SaleProvider saleProvider = context.read<SaleProvider>();
    final ExpenseProvider expenseProvider = context.read<ExpenseProvider>();
    final InternalTransferProvider transferProvider =
        context.read<InternalTransferProvider>();
    final ProductProvider productProvider = context.read<ProductProvider>();
    final StoreInventoryProvider storeInventoryProvider =
        context.read<StoreInventoryProvider>();

    // Load data from all providers
    await analyticsProvider.fetchSalesData(saleProvider);
    await analyticsProvider.fetchExpenseData(expenseProvider);
    await analyticsProvider.fetchInternalTransferData(transferProvider);
    await analyticsProvider.fetchInventoryData(productProvider);
    await analyticsProvider
        .fetchStoreInventoryAdjustmentData(storeInventoryProvider);
    await analyticsProvider.loadAnalyticsData();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey.shade50,
        appBar: AppBar(
          title: const Text('الإحصائيات والتقارير'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadAnalyticsData,
            ),
            IconButton(
              icon: const Icon(Icons.date_range),
              onPressed: _showDateRangePicker,
            ),
          ],
        ),
        body: Consumer<AnalyticsProvider>(
          builder: (BuildContext context, AnalyticsProvider analyticsProvider,
              Widget? child) {
            if (analyticsProvider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              );
            }

            if (analyticsProvider.error != null) {
              return _buildErrorWidget(analyticsProvider.error!);
            }

            return RefreshIndicator(
              onRefresh: _loadAnalyticsData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // Date Range Header
                    _buildDateRangeHeader(analyticsProvider),

                    const SizedBox(height: 20),

                    // KPI Cards Section
                    _buildKPISection(analyticsProvider),

                    const SizedBox(height: 20),

                    // Sales Distribution Chart
                    _buildSalesDistributionSection(analyticsProvider),

                    const SizedBox(height: 20),

                    // Inventory Distribution Chart
                    _buildInventoryDistributionSection(analyticsProvider),

                    const SizedBox(height: 20),

                    // Profit Summary
                    _buildProfitSummarySection(analyticsProvider),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDateRangeHeader(AnalyticsProvider provider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: <Widget>[
          const Text(
            'فترة التقرير',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_formatDate(provider.startDate)} - ${_formatDate(provider.endDate)}',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKPISection(AnalyticsProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'مؤشرات الأداء الرئيسية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        // الصف الأول: المبيعات
        Row(
          children: <Widget>[
            Expanded(
              child: _buildKPICard(
                'مبيعات الجملة',
                provider.formattedTotalWholesaleSales,
                Icons.business,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKPICard(
                'مبيعات التجزئة',
                provider.formattedTotalRetailSales,
                Icons.shopping_cart,
                Colors.orange,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثاني: الأرباح
        Row(
          children: <Widget>[
            Expanded(
              child: _buildKPICard(
                'ربح الجملة',
                provider.formattedTotalWholesaleProfit,
                Icons.trending_up,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKPICard(
                'الربح الفعلي للتجزئة',
                provider.formattedActualRetailProfit,
                Icons.account_balance_wallet,
                Colors.purple,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثالث: الأرباح المحتملة والخسائر
        Row(
          children: <Widget>[
            Expanded(
              child: _buildKPICard(
                'الربح المحتمل للتجزئة',
                provider.formattedPotentialRetailProfit,
                Icons.trending_up_outlined,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKPICard(
                'خسائر المخزون',
                provider.formattedTotalInventoryLosses,
                Icons.trending_down,
                Colors.red,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الرابع: المخزون
        Row(
          children: <Widget>[
            Expanded(
              child: _buildKPICard(
                'المخزن',
                '${provider.totalWarehouseQuantity} قطعة',
                Icons.warehouse,
                Colors.teal,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKPICard(
                'البقالة',
                '${provider.totalStoreQuantity} قطعة',
                Icons.store,
                Colors.indigo,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKPICard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: <Widget>[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesDistributionSection(AnalyticsProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'توزيع المبيعات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: <Widget>[
                        _buildDistributionItem(
                          'مبيعات الجملة',
                          provider.formattedTotalWholesaleSales,
                          Colors.blue,
                          provider.totalSales > 0
                              ? (provider.totalWholesaleSales /
                                  provider.totalSales *
                                  100)
                              : 0,
                        ),
                        const SizedBox(height: 8),
                        _buildDistributionItem(
                          'مبيعات التجزئة',
                          provider.formattedTotalRetailSales,
                          Colors.orange,
                          provider.totalSales > 0
                              ? (provider.totalRetailSales /
                                  provider.totalSales *
                                  100)
                              : 0,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: Container(
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Text(
                          'رسم بياني\n(قيد التطوير)',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDistributionItem(
      String title, String value, Color color, double percentage) {
    return Row(
      children: <Widget>[
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryDistributionSection(AnalyticsProvider provider) {
    final int totalInventory =
        provider.totalWarehouseQuantity + provider.totalStoreQuantity;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'توزيع المخزون',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: <Widget>[
                        _buildDistributionItem(
                          'المخزن',
                          '${provider.totalWarehouseQuantity} قطعة',
                          Colors.teal,
                          totalInventory > 0
                              ? (provider.totalWarehouseQuantity /
                                  totalInventory *
                                  100)
                              : 0,
                        ),
                        const SizedBox(height: 8),
                        _buildDistributionItem(
                          'البقالة',
                          '${provider.totalStoreQuantity} قطعة',
                          Colors.indigo,
                          totalInventory > 0
                              ? (provider.totalStoreQuantity /
                                  totalInventory *
                                  100)
                              : 0,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: Container(
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Text(
                          'رسم بياني\n(قيد التطوير)',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfitSummarySection(AnalyticsProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'ملخص الأرباح',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: <Widget>[
              _buildProfitItem(
                'إجمالي الأرباح',
                provider.formattedTotalProfit,
                Colors.green,
                Icons.trending_up,
              ),
              const Divider(height: 24, thickness: 0.5),
              _buildProfitItem(
                'إجمالي المصروفات',
                provider.formattedTotalExpenses,
                Colors.red,
                Icons.money_off,
              ),
              const Divider(height: 24, thickness: 0.5),
              _buildProfitItem(
                'صافي الربح',
                provider.formattedNetProfit,
                provider.netProfit >= 0 ? Colors.green : Colors.red,
                provider.netProfit >= 0
                    ? Icons.arrow_upward
                    : Icons.arrow_downward,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfitItem(
      String title, String value, Color color, IconData icon) {
    return Row(
      children: <Widget>[
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final AnalyticsProvider analyticsProvider =
        context.read<AnalyticsProvider>();

    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: analyticsProvider.startDate,
        end: analyticsProvider.endDate,
      ),
    );

    if (picked != null) {
      analyticsProvider.setDateRange(picked.start, picked.end);
      await _loadAnalyticsData();
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
