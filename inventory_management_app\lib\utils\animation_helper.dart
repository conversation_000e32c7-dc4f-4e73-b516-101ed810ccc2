import 'package:flutter/material.dart';
import 'dart:math';

/// فئة مساعدة للرسوم المتحركة
class AnimationHelper {
  // مدة الرسوم المتحركة
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 500);
  static const Duration verySlowDuration = Duration(milliseconds: 800);

  // منحنيات الرسوم المتحركة
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.bounceOut;
  static const Curve elasticCurve = Curves.elasticOut;
  static const Curve fastOutSlowIn = Curves.fastOutSlowIn;

  /// رسم متحرك للظهور التدريجي
  static Widget fadeIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: begin, end: end),
      duration: duration,
      curve: curve,
      builder: (BuildContext context, double value, Widget? child) {
        return Opacity(
          opacity: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للانزلاق
  static Widget slideIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    Offset begin = const Offset(0, 1),
    Offset end = Offset.zero,
  }) {
    return TweenAnimationBuilder<Offset>(
      tween: Tween(begin: begin, end: end),
      duration: duration,
      curve: curve,
      builder: (BuildContext context, Offset value, Widget? child) {
        return Transform.translate(
          offset: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للتكبير
  static Widget scaleIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: begin, end: end),
      duration: duration,
      curve: curve,
      builder: (BuildContext context, double value, Widget? child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للدوران
  static Widget rotateIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: begin, end: end),
      duration: duration,
      curve: curve,
      builder: (BuildContext context, double value, Widget? child) {
        return Transform.rotate(
          angle: value * 2 * 3.14159, // دورة كاملة
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك مركب (ظهور + انزلاق)
  static Widget fadeSlideIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    Offset slideBegin = const Offset(0, 0.3),
    double fadeBegin = 0.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      curve: curve,
      builder: (BuildContext context, double value, Widget? child) {
        return Opacity(
          opacity: fadeBegin + (1.0 - fadeBegin) * value,
          child: Transform.translate(
            offset: Offset(
              slideBegin.dx * (1.0 - value),
              slideBegin.dy * (1.0 - value),
            ),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للقائمة
  static Widget listItemAnimation({
    required Widget child,
    required int index,
    Duration duration = normalDuration,
    Duration delay = const Duration(milliseconds: 50),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration + (delay * index),
      curve: Curves.easeOutBack,
      builder: (BuildContext context, double value, Widget? child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1.0 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للبطاقات المتتالية
  static Widget staggeredAnimation({
    required Widget child,
    required int index,
    Duration duration = normalDuration,
    Duration staggerDelay = const Duration(milliseconds: 100),
  }) {
    final Duration delay = staggerDelay * index;

    return AnimatedBuilder(
      animation: const AlwaysStoppedAnimation(0),
      builder: (BuildContext context, _) {
        return TweenAnimationBuilder<double>(
          tween: Tween(begin: 0.0, end: 1.0),
          duration: duration,
          curve: Curves.easeOutCubic,
          builder: (BuildContext context, double value, Widget? child) {
            return Transform.translate(
              offset: Offset(0, 30 * (1.0 - value)),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: child,
        );
      },
    );
  }

  /// رسم متحرك للنبضة
  static Widget pulse({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1000),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: minScale, end: maxScale),
      duration: duration,
      curve: Curves.easeInOut,
      builder: (BuildContext context, double value, Widget? child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      onEnd: () {
        // إعادة تشغيل الرسم المتحرك
      },
      child: child,
    );
  }

  /// رسم متحرك للاهتزاز
  static Widget shake({
    required Widget child,
    Duration duration = const Duration(milliseconds: 500),
    double offset = 10.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      curve: Curves.elasticIn,
      builder: (BuildContext context, double value, Widget? child) {
        final double shakeValue = sin(value * 4 * 3.14159) * offset * (1.0 - value);
        return Transform.translate(
          offset: Offset(shakeValue, 0),
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للانتقال بين الصفحات
  static PageRouteBuilder createPageRoute({
    required Widget page,
    PageTransitionType type = PageTransitionType.slideFromRight,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
  }) {
    return PageRouteBuilder(
      pageBuilder: (BuildContext context, Animation<double> animation,
              Animation<double> secondaryAnimation) =>
          page,
      transitionDuration: duration,
      transitionsBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation, Widget child) {
        switch (type) {
          case PageTransitionType.fade:
            return FadeTransition(opacity: animation, child: child);

          case PageTransitionType.slideFromRight:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                    .chain(CurveTween(curve: curve)),
              ),
              child: child,
            );

          case PageTransitionType.slideFromLeft:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(-1.0, 0.0), end: Offset.zero)
                    .chain(CurveTween(curve: curve)),
              ),
              child: child,
            );

          case PageTransitionType.slideFromBottom:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(0.0, 1.0), end: Offset.zero)
                    .chain(CurveTween(curve: curve)),
              ),
              child: child,
            );

          case PageTransitionType.scale:
            return ScaleTransition(
              scale: animation.drive(
                Tween(begin: 0.0, end: 1.0).chain(CurveTween(curve: curve)),
              ),
              child: child,
            );

          case PageTransitionType.rotation:
            return RotationTransition(
              turns: animation.drive(
                Tween(begin: 0.0, end: 1.0).chain(CurveTween(curve: curve)),
              ),
              child: child,
            );
        }
      },
    );
  }

  /// رسم متحرك للتحميل
  static Widget loadingAnimation({
    Color? color,
    double size = 50.0,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? Colors.blue,
        ),
        strokeWidth: 3.0,
      ),
    );
  }

  /// رسم متحرك للنجاح
  static Widget successAnimation({
    Color? color,
    double size = 50.0,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      curve: Curves.elasticOut,
      builder: (BuildContext context, double value, Widget? child) {
        return Transform.scale(
          scale: value,
          child: Icon(
            Icons.check_circle,
            color: color ?? Colors.green,
            size: size,
          ),
        );
      },
    );
  }

  /// رسم متحرك للخطأ
  static Widget errorAnimation({
    Color? color,
    double size = 50.0,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      curve: Curves.elasticOut,
      builder: (BuildContext context, double value, Widget? child) {
        return Transform.scale(
          scale: value,
          child: Icon(
            Icons.error,
            color: color ?? Colors.red,
            size: size,
          ),
        );
      },
    );
  }

  /// رسم متحرك للعد التنازلي
  static Widget countdownAnimation({
    required int count,
    Duration duration = const Duration(seconds: 1),
    TextStyle? textStyle,
  }) {
    return TweenAnimationBuilder<int>(
      tween: IntTween(begin: count, end: 0),
      duration: duration * count,
      builder: (BuildContext context, int value, Widget? child) {
        return Text(
          value.toString(),
          style: textStyle ?? const TextStyle(fontSize: 48),
        );
      },
    );
  }

  /// رسم متحرك للشريط المتقدم
  static Widget progressAnimation({
    required double progress,
    Duration duration = normalDuration,
    Color? color,
    Color? backgroundColor,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: progress),
      duration: duration,
      curve: Curves.easeInOut,
      builder: (BuildContext context, double value, Widget? child) {
        return LinearProgressIndicator(
          value: value,
          valueColor: AlwaysStoppedAnimation<Color>(
            color ?? Colors.blue,
          ),
          backgroundColor: backgroundColor ?? Colors.grey[300],
        );
      },
    );
  }
}

/// أنواع انتقالات الصفحات
enum PageTransitionType {
  fade,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  scale,
  rotation,
}

/// ويدجت للرسوم المتحركة المخصصة
class CustomAnimatedWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final AnimationType type;

  const CustomAnimatedWidget({
    super.key,
    required this.child,
    this.duration = AnimationHelper.normalDuration,
    this.curve = AnimationHelper.defaultCurve,
    this.type = AnimationType.fadeIn,
  });

  @override
  State<CustomAnimatedWidget> createState() => _CustomAnimatedWidgetState();
}

class _CustomAnimatedWidgetState extends State<CustomAnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.type) {
      case AnimationType.fadeIn:
        return FadeTransition(
          opacity: _animation,
          child: widget.child,
        );
      case AnimationType.slideIn:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(_animation),
          child: widget.child,
        );
      case AnimationType.scaleIn:
        return ScaleTransition(
          scale: _animation,
          child: widget.child,
        );
    }
  }
}

/// أنواع الرسوم المتحركة
enum AnimationType {
  fadeIn,
  slideIn,
  scaleIn,
}
