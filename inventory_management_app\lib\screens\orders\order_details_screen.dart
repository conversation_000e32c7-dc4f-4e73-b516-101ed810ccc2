import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/order.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/models/order_item.dart';
import 'package:sqflite/sqflite.dart';

class OrderDetailsScreen extends StatefulWidget {
  final Order order;

  const OrderDetailsScreen({super.key, required this.order});

  @override
  _OrderDetailsScreenState createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  List<OrderItem> _orderItems = <OrderItem>[];

  @override
  void initState() {
    super.initState();
    _fetchOrderItems();
  }

  Future<void> _fetchOrderItems() async {
    final Database db = await DatabaseService().database;
    final List<Map<String, dynamic>> maps = await db.query(
      'order_items',
      where: 'orderId = ?',
      whereArgs: <Object?>[widget.order.id],
    );
    setState(() {
      _orderItems = List.generate(maps.length, (int i) {
        return OrderItem.fromMap(maps[i]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('Order ID: ${widget.order.id}'),
            Text('Customer ID: ${widget.order.customerId}'),
            Text('Order Date: ${widget.order.date ?? 'N/A'}'),
            Text(
                'Total Amount: \$${widget.order.total?.toStringAsFixed(2) ?? '0.00'}'),
            const SizedBox(height: 16),
            const Text(
              'Order Items:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: _orderItems.length,
                itemBuilder: (BuildContext context, int index) {
                  final OrderItem orderItem = _orderItems[index];
                  return ListTile(
                    title: Text('Product ID: ${orderItem.productId}'),
                    subtitle: Text(
                        'Quantity: ${orderItem.quantity}, Price: \$${orderItem.price?.toStringAsFixed(2) ?? '0.00'}'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
