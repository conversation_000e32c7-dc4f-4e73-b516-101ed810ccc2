/// Model class representing a daily business summary
class DailySummary {
  /// Unique identifier for the daily summary
  int? id;
  
  /// Date of the summary
  String? date;
  
  /// Total sales for the day
  double? sales;
  
  /// Total purchases for the day
  double? purchases;
  
  /// Total expenses for the day
  double? expenses;

  /// Constructor for creating a DailySummary instance
  DailySummary({
    this.id,
    this.date,
    this.sales,
    this.purchases,
    this.expenses,
  });

  /// Converts the DailySummary instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'date': date,
      'sales': sales,
      'purchases': purchases,
      'expenses': expenses,
    };
  }

  /// Creates a DailySummary instance from a Map (typically from database)
  factory DailySummary.fromMap(Map<String, dynamic> map) {
    return DailySummary(
      id: map['id'] as int?,
      date: map['date'] as String?,
      sales: map['sales']?.toDouble(),
      purchases: map['purchases']?.toDouble(),
      expenses: map['expenses']?.toDouble(),
    );
  }

  /// Calculate net profit for the day
  double get netProfit => (sales ?? 0) - (purchases ?? 0) - (expenses ?? 0);

  @override
  String toString() {
    return 'DailySummary{id: $id, date: $date, sales: $sales, '
        'purchases: $purchases, expenses: $expenses}';
  }
}
