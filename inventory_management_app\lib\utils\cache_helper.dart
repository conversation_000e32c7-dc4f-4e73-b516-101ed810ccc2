import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_constants.dart';

/// فئة مساعدة للتخزين المؤقت
class CacheHelper {
  static final Map<String, CacheEntry> _memoryCache = <String, CacheEntry>{};
  static const Duration _defaultExpiry = Duration(hours: 1);
  static const int _maxMemoryCacheSize = 100;

  /// تهيئة التخزين المؤقت
  static Future<void> initialize() async {
    await _cleanExpiredCache();
  }

  /// حفظ البيانات في التخزين المؤقت
  static Future<void> put(
    String key,
    dynamic data, {
    Duration? expiry,
    bool memoryOnly = false,
  }) async {
    final DateTime expiryTime = DateTime.now().add(expiry ?? _defaultExpiry);
    final CacheEntry entry = CacheEntry(
      key: key,
      data: data,
      expiryTime: expiryTime,
      createdAt: DateTime.now(),
    );

    // حفظ في الذاكرة
    _memoryCache[key] = entry;
    _limitMemoryCacheSize();

    // حفظ في القرص إذا لم يكن للذاكرة فقط
    if (!memoryOnly && !kIsWeb) {
      await _saveToDisk(key, entry);
    }
  }

  /// استرجاع البيانات من التخزين المؤقت
  static Future<T?> get<T>(String key) async {
    // البحث في الذاكرة أولاً
    final CacheEntry? memoryEntry = _memoryCache[key];
    if (memoryEntry != null && !memoryEntry.isExpired) {
      return memoryEntry.data as T?;
    }

    // إزالة البيانات المنتهية الصلاحية من الذاكرة
    if (memoryEntry?.isExpired == true) {
      _memoryCache.remove(key);
    }

    // البحث في القرص
    if (!kIsWeb) {
      final CacheEntry? diskEntry = await _loadFromDisk(key);
      if (diskEntry != null && !diskEntry.isExpired) {
        // إعادة تحميل في الذاكرة
        _memoryCache[key] = diskEntry;
        return diskEntry.data as T?;
      }

      // إزالة البيانات المنتهية الصلاحية من القرص
      if (diskEntry?.isExpired == true) {
        await _removeFromDisk(key);
      }
    }

    return null;
  }

  /// التحقق من وجود البيانات في التخزين المؤقت
  static Future<bool> has(String key) async {
    final data = await get(key);
    return data != null;
  }

  /// إزالة البيانات من التخزين المؤقت
  static Future<void> remove(String key) async {
    _memoryCache.remove(key);

    if (!kIsWeb) {
      await _removeFromDisk(key);
    }
  }

  /// مسح جميع البيانات المؤقتة
  static Future<void> clear() async {
    _memoryCache.clear();

    if (!kIsWeb) {
      await _clearDiskCache();
    }
  }

  /// مسح البيانات المنتهية الصلاحية
  static Future<void> clearExpired() async {
    // مسح من الذاكرة
    _memoryCache.removeWhere((String key, CacheEntry entry) => entry.isExpired);

    // مسح من القرص
    if (!kIsWeb) {
      await _cleanExpiredCache();
    }
  }

  /// الحصول على حجم التخزين المؤقت
  static Future<CacheSize> getCacheSize() async {
    final int memorySize = _memoryCache.length;
    int diskSize = 0;

    if (!kIsWeb) {
      try {
        final Directory cacheDir = await _getCacheDirectory();
        if (await cacheDir.exists()) {
          final List<FileSystemEntity> files = await cacheDir.list().toList();
          diskSize = files.length;
        }
      } catch (e) {
        // تجاهل الأخطاء
      }
    }

    return CacheSize(
      memoryEntries: memorySize,
      diskEntries: diskSize,
    );
  }

  /// الحصول على إحصائيات التخزين المؤقت
  static Future<CacheStats> getStats() async {
    final CacheSize size = await getCacheSize();
    final DateTime now = DateTime.now();

    int hits = 0;
    int misses = 0;
    int expired = 0;

    for (final CacheEntry entry in _memoryCache.values) {
      if (entry.isExpired) {
        expired++;
      } else {
        hits++;
      }
    }

    return CacheStats(
      totalEntries: size.memoryEntries + size.diskEntries,
      memoryEntries: size.memoryEntries,
      diskEntries: size.diskEntries,
      hits: hits,
      misses: misses,
      expired: expired,
    );
  }

  /// تحديد حجم التخزين المؤقت في الذاكرة
  static void _limitMemoryCacheSize() {
    if (_memoryCache.length <= _maxMemoryCacheSize) return;

    // إزالة أقدم البيانات
    final List<MapEntry<String, CacheEntry>> entries =
        _memoryCache.entries.toList();
    entries.sort(
        (MapEntry<String, CacheEntry> a, MapEntry<String, CacheEntry> b) =>
            a.value.createdAt.compareTo(b.value.createdAt));

    final Iterable<MapEntry<String, CacheEntry>> toRemove =
        entries.take(_memoryCache.length - _maxMemoryCacheSize);
    for (final MapEntry<String, CacheEntry> entry in toRemove) {
      _memoryCache.remove(entry.key);
    }
  }

  /// حفظ البيانات في القرص
  static Future<void> _saveToDisk(String key, CacheEntry entry) async {
    try {
      final Directory cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final File file = File('${cacheDir.path}/${_sanitizeKey(key)}.cache');
      final String jsonData = json.encode(entry.toMap());
      await file.writeAsString(jsonData);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حفظ التخزين المؤقت: $e');
      }
    }
  }

  /// تحميل البيانات من القرص
  static Future<CacheEntry?> _loadFromDisk(String key) async {
    try {
      final Directory cacheDir = await _getCacheDirectory();
      final File file = File('${cacheDir.path}/${_sanitizeKey(key)}.cache');

      if (!await file.exists()) return null;

      final String jsonData = await file.readAsString();
      final Map<String, dynamic> map =
          json.decode(jsonData) as Map<String, dynamic>;
      return CacheEntry.fromMap(map);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل التخزين المؤقت: $e');
      }
      return null;
    }
  }

  /// إزالة البيانات من القرص
  static Future<void> _removeFromDisk(String key) async {
    try {
      final Directory cacheDir = await _getCacheDirectory();
      final File file = File('${cacheDir.path}/${_sanitizeKey(key)}.cache');

      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حذف التخزين المؤقت: $e');
      }
    }
  }

  /// مسح جميع ملفات التخزين المؤقت
  static Future<void> _clearDiskCache() async {
    try {
      final Directory cacheDir = await _getCacheDirectory();

      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في مسح التخزين المؤقت: $e');
      }
    }
  }

  /// تنظيف البيانات المنتهية الصلاحية
  static Future<void> _cleanExpiredCache() async {
    try {
      final Directory cacheDir = await _getCacheDirectory();

      if (!await cacheDir.exists()) return;

      final List<FileSystemEntity> files = await cacheDir.list().toList();

      for (final FileSystemEntity file in files) {
        if (file is File && file.path.endsWith('.cache')) {
          try {
            final String jsonData = await file.readAsString();
            final Map<String, dynamic> map =
                json.decode(jsonData) as Map<String, dynamic>;
            final CacheEntry entry = CacheEntry.fromMap(map);

            if (entry.isExpired) {
              await file.delete();
            }
          } catch (e) {
            // حذف الملفات التالفة
            await file.delete();
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تنظيف التخزين المؤقت: $e');
      }
    }
  }

  /// الحصول على مجلد التخزين المؤقت
  static Future<Directory> _getCacheDirectory() async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    return Directory('${appDir.path}/${AppConstants.cacheFolder}');
  }

  /// تنظيف اسم المفتاح
  static String _sanitizeKey(String key) {
    return key.replaceAll(RegExp(r'[^\w\-_.]'), '_');
  }

  /// تخزين مؤقت للصور
  static Future<void> cacheImage(String url, List<int> imageData) async {
    await put('image_$url', imageData, expiry: const Duration(days: 7));
  }

  /// استرجاع صورة من التخزين المؤقت
  static Future<List<int>?> getCachedImage(String url) async {
    return await get<List<int>>('image_$url');
  }

  /// تخزين مؤقت لاستجابات API
  static Future<void> cacheApiResponse(
    String endpoint,
    Map<String, dynamic> response, {
    Duration? expiry,
  }) async {
    await put(
      'api_$endpoint',
      response,
      expiry: expiry ?? const Duration(minutes: 15),
    );
  }

  /// استرجاع استجابة API من التخزين المؤقت
  static Future<Map<String, dynamic>?> getCachedApiResponse(
      String endpoint) async {
    return await get<Map<String, dynamic>>('api_$endpoint');
  }

  /// تخزين مؤقت للبيانات المحسوبة
  static Future<void> cacheCalculation(
    String calculationKey,
    dynamic result, {
    Duration? expiry,
  }) async {
    await put(
      'calc_$calculationKey',
      result,
      expiry: expiry ?? const Duration(hours: 24),
    );
  }

  /// استرجاع نتيجة حساب من التخزين المؤقت
  static Future<T?> getCachedCalculation<T>(String calculationKey) async {
    return await get<T>('calc_$calculationKey');
  }
}

/// إدخال التخزين المؤقت
class CacheEntry {
  final String key;
  final dynamic data;
  final DateTime expiryTime;
  final DateTime createdAt;

  CacheEntry({
    required this.key,
    required this.data,
    required this.expiryTime,
    required this.createdAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiryTime);

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'key': key,
      'data': data,
      'expiryTime': expiryTime.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory CacheEntry.fromMap(Map<String, dynamic> map) {
    return CacheEntry(
      key: map['key'],
      data: map['data'],
      expiryTime: DateTime.parse(map['expiryTime']),
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}

/// حجم التخزين المؤقت
class CacheSize {
  final int memoryEntries;
  final int diskEntries;

  CacheSize({
    required this.memoryEntries,
    required this.diskEntries,
  });

  int get totalEntries => memoryEntries + diskEntries;
}

/// إحصائيات التخزين المؤقت
class CacheStats {
  final int totalEntries;
  final int memoryEntries;
  final int diskEntries;
  final int hits;
  final int misses;
  final int expired;

  CacheStats({
    required this.totalEntries,
    required this.memoryEntries,
    required this.diskEntries,
    required this.hits,
    required this.misses,
    required this.expired,
  });

  double get hitRate {
    final int total = hits + misses;
    return total > 0 ? (hits / total) * 100 : 0.0;
  }
}
