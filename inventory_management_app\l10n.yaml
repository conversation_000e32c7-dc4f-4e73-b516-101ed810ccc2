# ملف إعدادات الترجمة والتوطين لأسامة ماركت
# يحدد كيفية إنتاج ملفات الترجمة من ملفات ARB

# مجلد ملفات ARB
arb-dir: lib/l10n

# ملف القالب الأساسي (العربية)
template-arb-file: app_ar.arb

# مجلد الإخراج للملفات المولدة
output-dir: lib/generated

# اسم الكلاس المولد
output-class: AppLocalizations

# ملف الإخراج الرئيسي
output-localization-file: app_localizations.dart

# ملف إخراج المندوبين
output-localization-delegate-file: app_localizations_delegate.dart

# اللغات المدعومة
preferred-supported-locales: ["ar", "en"]

# إعدادات إضافية
header-comment: |
  /// ملفات الترجمة المولدة تلقائياً لأسامة ماركت
  /// لا تقم بتعديل هذه الملفات مباشرة
  /// استخدم ملفات ARB في مجلد lib/l10n بدلاً من ذلك

# تفعيل الترجمة الاصطناعية
synthetic-package: false

# استخدام الرسائل المؤجلة
use-deferred-loading: false
