import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:provider/provider.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../config/app_dimensions.dart';
import '../../widgets/enhanced_dashboard_widgets.dart';
import '../../widgets/custom_buttons.dart';
import '../../widgets/app_scaffold.dart';
import '../../utils/formatters.dart';
import '../../utils/date_helper.dart';
import '../../utils/snackbar_helper.dart';
import '../../providers/product_provider.dart';
import '../../providers/sale_provider.dart';
import '../../providers/purchase_provider.dart';
import '../../providers/customer_provider.dart';

/// شاشة التقارير والإحصائيات
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _selectedDateRange = DateTimeRange(
      start: DateHelper.startOfMonth,
      end: DateHelper.endOfMonth,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'التقارير والإحصائيات',
      currentRoute: '/reports',
      actions: <Widget>[
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportCurrentReport,
          tooltip: 'تصدير التقرير',
        ),
        IconButton(
          icon: const Icon(Icons.print),
          onPressed: _printCurrentReport,
          tooltip: 'طباعة التقرير',
        ),
      ],
      body: Container(
        color: AppColors.background,
        child: Column(
          children: <Widget>[
            _buildDateRangeSelector(),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: <Widget>[
                  _buildSalesReports(),
                  _buildInventoryReports(),
                  _buildFinancialReports(),
                  _buildCustomerReports(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: <Widget>[
          const Icon(
            Icons.date_range,
            color: AppColors.primary,
          ),
          const SizedBox(width: AppDimensions.paddingS),
          Expanded(
            child: Text(
              _selectedDateRange != null
                  ? '${DateHelper.formatDate(_selectedDateRange!.start)} - ${DateHelper.formatDate(_selectedDateRange!.end)}'
                  : 'اختر الفترة الزمنية',
              style: AppStyles.bodyMedium,
            ),
          ),
          CustomTextButton(
            text: 'تغيير',
            onPressed: _selectDateRange,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const <Widget>[
          Tab(text: 'المبيعات'),
          Tab(text: 'المخزون'),
          Tab(text: 'المالية'),
          Tab(text: 'العملاء'),
        ],
      ),
    );
  }

  Widget _buildSalesReports() {
    return Consumer<SaleProvider>(
      builder:
          (BuildContext context, SaleProvider saleProvider, Widget? child) {
        final List<dynamic> sales = _getFilteredSales(saleProvider.sales);
        final int totalSales = sales.length;
        final double totalRevenue = sales.fold<double>(
            0, (double sum, sale) => sum + (sale.total ?? 0));
        final int completedSales =
            sales.where((sale) => sale.status == 'completed').length;
        final double averageSale =
            totalSales > 0 ? totalRevenue / totalSales : 0.0;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'تقرير المبيعات',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingM),

              // بطاقات الإحصائيات
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.2,
                crossAxisSpacing: AppDimensions.paddingM,
                mainAxisSpacing: AppDimensions.paddingM,
                children: <Widget>[
                  EnhancedStatCard(
                    title: 'إجمالي المبيعات',
                    value: '$totalSales',
                    icon: Icons.receipt_long,
                    color: AppColors.primary,
                    subtitle: 'فاتورة',
                  ),
                  EnhancedStatCard(
                    title: 'إجمالي الإيرادات',
                    value: Formatters.formatCurrency(totalRevenue),
                    icon: Icons.monetization_on,
                    color: AppColors.success,
                    subtitle: 'ريال سعودي',
                  ),
                  EnhancedStatCard(
                    title: 'المبيعات المكتملة',
                    value: '$completedSales',
                    icon: Icons.check_circle,
                    color: AppColors.info,
                    subtitle: 'فاتورة مكتملة',
                  ),
                  EnhancedStatCard(
                    title: 'متوسط قيمة البيع',
                    value: Formatters.formatCurrency(averageSale),
                    icon: Icons.trending_up,
                    color: AppColors.accent,
                    subtitle: 'ريال سعودي',
                  ),
                ],
              ),

              const SizedBox(height: AppDimensions.paddingL),

              // أفضل المنتجات مبيعاً
              _buildTopSellingProducts(),

              const SizedBox(height: AppDimensions.paddingL),

              // المبيعات اليومية
              _buildDailySalesChart(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInventoryReports() {
    return Consumer<ProductProvider>(
      builder: (BuildContext context, ProductProvider productProvider,
          Widget? child) {
        final List<Product> products = productProvider.products;
        final int totalProducts = products.length;
        final int lowStockProducts = products
            .where((Product p) => (p.quantity ?? 0) <= (p.minLevel ?? 10))
            .length;
        final int outOfStockProducts =
            products.where((Product p) => (p.quantity ?? 0) <= 0).length;
        final double totalValue = products.fold<double>(
            0,
            (double sum, Product product) =>
                sum + ((product.quantity ?? 0) * (product.purchasePrice ?? 0)));

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'تقرير المخزون',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingM),

              // بطاقات الإحصائيات
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.2,
                crossAxisSpacing: AppDimensions.paddingM,
                mainAxisSpacing: AppDimensions.paddingM,
                children: <Widget>[
                  EnhancedStatCard(
                    title: 'إجمالي المنتجات',
                    value: '$totalProducts',
                    icon: Icons.inventory_2,
                    color: AppColors.primary,
                    subtitle: 'منتج',
                  ),
                  EnhancedStatCard(
                    title: 'قيمة المخزون',
                    value: Formatters.formatCurrency(totalValue),
                    icon: Icons.account_balance_wallet,
                    color: AppColors.success,
                    subtitle: 'ريال سعودي',
                  ),
                  EnhancedStatCard(
                    title: 'مخزون منخفض',
                    value: '$lowStockProducts',
                    icon: Icons.warning,
                    color: AppColors.warning,
                    subtitle: 'منتج',
                  ),
                  EnhancedStatCard(
                    title: 'نفد المخزون',
                    value: '$outOfStockProducts',
                    icon: Icons.error,
                    color: AppColors.error,
                    subtitle: 'منتج',
                  ),
                ],
              ),

              const SizedBox(height: AppDimensions.paddingL),

              // المنتجات التي تحتاج إعادة تخزين
              _buildLowStockProducts(),

              const SizedBox(height: AppDimensions.paddingL),

              // توزيع المنتجات حسب الفئة
              _buildCategoryDistribution(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinancialReports() {
    return Consumer2<SaleProvider, PurchaseProvider>(
      builder: (BuildContext context, SaleProvider saleProvider,
          PurchaseProvider purchaseProvider, Widget? child) {
        final List<dynamic> sales = _getFilteredSales(saleProvider.sales);
        final List<dynamic> purchases =
            _getFilteredPurchases(purchaseProvider.purchases);

        final double totalRevenue = sales.fold<double>(
            0, (double sum, sale) => sum + (sale.total ?? 0));
        final double totalExpenses = purchases.fold<double>(
            0, (double sum, purchase) => sum + (purchase.total ?? 0));
        final double netProfit = totalRevenue - totalExpenses;
        final num profitMargin =
            totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'التقرير المالي',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingM),

              // بطاقات الإحصائيات
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.2,
                crossAxisSpacing: AppDimensions.paddingM,
                mainAxisSpacing: AppDimensions.paddingM,
                children: <Widget>[
                  EnhancedStatCard(
                    title: 'إجمالي الإيرادات',
                    value: Formatters.formatCurrency(totalRevenue),
                    icon: Icons.trending_up,
                    color: AppColors.success,
                    subtitle: 'ريال سعودي',
                  ),
                  EnhancedStatCard(
                    title: 'إجمالي المصروفات',
                    value: Formatters.formatCurrency(totalExpenses),
                    icon: Icons.trending_down,
                    color: AppColors.error,
                    subtitle: 'ريال سعودي',
                  ),
                  EnhancedStatCard(
                    title: 'صافي الربح',
                    value: Formatters.formatCurrency(netProfit),
                    icon: Icons.account_balance,
                    color: netProfit >= 0 ? AppColors.success : AppColors.error,
                    subtitle: 'ريال سعودي',
                  ),
                  EnhancedStatCard(
                    title: 'هامش الربح',
                    value: '${profitMargin.toStringAsFixed(1)}%',
                    icon: Icons.percent,
                    color: AppColors.info,
                    subtitle: 'نسبة مئوية',
                  ),
                ],
              ),

              const SizedBox(height: AppDimensions.paddingL),

              // مخطط الإيرادات والمصروفات
              _buildRevenueExpenseChart(),

              const SizedBox(height: AppDimensions.paddingL),

              // تحليل الربحية
              _buildProfitabilityAnalysis(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCustomerReports() {
    return Consumer2<CustomerProvider, SaleProvider>(
      builder: (BuildContext context, CustomerProvider customerProvider,
          SaleProvider saleProvider, Widget? child) {
        final List<Customer> customers = customerProvider.customers;
        final List<dynamic> sales = _getFilteredSales(saleProvider.sales);

        final int totalCustomers = customers.length;
        final int activeCustomers =
            sales.map((sale) => sale.customerId).toSet().length;
        final double totalDebt = customers.fold<double>(
            0,
            (double sum, Customer customer) =>
                sum +
                ((customer.balance ?? 0) < 0
                    ? (customer.balance ?? 0).abs()
                    : 0));
        final double totalCredit = customers.fold<double>(
            0,
            (double sum, Customer customer) =>
                sum +
                ((customer.balance ?? 0) > 0 ? customer.balance ?? 0 : 0));

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'تقرير العملاء',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingM),

              // بطاقات الإحصائيات
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.2,
                crossAxisSpacing: AppDimensions.paddingM,
                mainAxisSpacing: AppDimensions.paddingM,
                children: <Widget>[
                  EnhancedStatCard(
                    title: 'إجمالي العملاء',
                    value: '$totalCustomers',
                    icon: Icons.people,
                    color: AppColors.primary,
                    subtitle: 'عميل',
                  ),
                  EnhancedStatCard(
                    title: 'العملاء النشطون',
                    value: '$activeCustomers',
                    icon: Icons.person_pin,
                    color: AppColors.success,
                    subtitle: 'عميل نشط',
                  ),
                  EnhancedStatCard(
                    title: 'إجمالي الديون',
                    value: Formatters.formatCurrency(totalDebt),
                    icon: Icons.money_off,
                    color: AppColors.error,
                    subtitle: 'ريال سعودي',
                  ),
                  EnhancedStatCard(
                    title: 'إجمالي الأرصدة',
                    value: Formatters.formatCurrency(totalCredit),
                    icon: Icons.account_balance_wallet,
                    color: AppColors.info,
                    subtitle: 'ريال سعودي',
                  ),
                ],
              ),

              const SizedBox(height: AppDimensions.paddingL),

              // أفضل العملاء
              _buildTopCustomers(),

              const SizedBox(height: AppDimensions.paddingL),

              // العملاء المدينون
              _buildDebtorCustomers(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTopSellingProducts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'أفضل المنتجات مبيعاً',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            // TODO: إضافة قائمة المنتجات الأكثر مبيعاً
            Text(
              'لا توجد بيانات متاحة',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailySalesChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'المبيعات اليومية',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'سيتم إضافة المخطط قريباً',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLowStockProducts() {
    return Consumer<ProductProvider>(
      builder: (BuildContext context, ProductProvider productProvider,
          Widget? child) {
        final List<Product> lowStockProducts = productProvider.products
            .where((Product p) => (p.quantity ?? 0) <= (p.minLevel ?? 10))
            .take(5)
            .toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'المنتجات التي تحتاج إعادة تخزين',
                  style: AppStyles.titleMedium,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                if (lowStockProducts.isEmpty)
                  Text(
                    'جميع المنتجات متوفرة بكميات كافية',
                    style: AppStyles.bodyMedium.copyWith(
                      color: AppColors.success,
                    ),
                  )
                else
                  ...lowStockProducts.map((Product product) => ListTile(
                        leading: const Icon(
                          Icons.warning,
                          color: AppColors.warning,
                        ),
                        title: Text(product.name),
                        subtitle:
                            Text('الكمية المتبقية: ${product.quantity ?? 0}'),
                        trailing: Text(
                          'الحد الأدنى: ${product.minLevel ?? 0}',
                          style: AppStyles.bodySmall,
                        ),
                      )),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryDistribution() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'توزيع المنتجات حسب الفئة',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'سيتم إضافة المخطط قريباً',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueExpenseChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'مخطط الإيرادات والمصروفات',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'سيتم إضافة المخطط قريباً',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitabilityAnalysis() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'تحليل الربحية',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'سيتم إضافة تحليل مفصل للربحية',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCustomers() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'أفضل العملاء',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'لا توجد بيانات متاحة',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebtorCustomers() {
    return Consumer<CustomerProvider>(
      builder: (BuildContext context, CustomerProvider customerProvider,
          Widget? child) {
        final List<Customer> debtorCustomers = customerProvider.customers
            .where((Customer c) => (c.balance ?? 0) < 0)
            .take(5)
            .toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'العملاء المدينون',
                  style: AppStyles.titleMedium,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                if (debtorCustomers.isEmpty)
                  Text(
                    'لا يوجد عملاء مدينون',
                    style: AppStyles.bodyMedium.copyWith(
                      color: AppColors.success,
                    ),
                  )
                else
                  ...debtorCustomers.map((Customer customer) => ListTile(
                        leading: const Icon(
                          Icons.person,
                          color: AppColors.error,
                        ),
                        title: Text(customer.name),
                        subtitle: Text(customer.phone ?? ''),
                        trailing: Text(
                          Formatters.formatCurrency(
                              (customer.balance ?? 0).abs()),
                          style: AppStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )),
              ],
            ),
          ),
        );
      },
    );
  }

  List<dynamic> _getFilteredSales(List<dynamic> sales) {
    if (_selectedDateRange == null) return sales;

    return sales.where((dynamic sale) {
      final dynamic saleDate = sale.date;
      if (saleDate == null) return false;

      // Handle both DateTime and String dates
      DateTime? parsedDate;
      if (saleDate is DateTime) {
        parsedDate = saleDate;
      } else if (saleDate is String) {
        try {
          parsedDate = DateTime.parse(saleDate);
        } catch (e) {
          return false;
        }
      } else {
        return false;
      }

      return parsedDate.isAfter(
              _selectedDateRange!.start.subtract(const Duration(days: 1))) &&
          parsedDate
              .isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
  }

  List<dynamic> _getFilteredPurchases(List<dynamic> purchases) {
    if (_selectedDateRange == null) return purchases;

    return purchases.where((dynamic purchase) {
      final dynamic purchaseDate = purchase.date;
      if (purchaseDate == null) return false;

      // Handle both DateTime and String dates
      DateTime? parsedDate;
      if (purchaseDate is DateTime) {
        parsedDate = purchaseDate;
      } else if (purchaseDate is String) {
        try {
          parsedDate = DateTime.parse(purchaseDate);
        } catch (e) {
          return false;
        }
      } else {
        return false;
      }

      return parsedDate.isAfter(
              _selectedDateRange!.start.subtract(const Duration(days: 1))) &&
          parsedDate
              .isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
  }

  void _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _exportCurrentReport() {
    final int currentTab = _tabController.index;
    String reportType;

    switch (currentTab) {
      case 0:
        reportType = 'المبيعات';
        break;
      case 1:
        reportType = 'المخزون';
        break;
      case 2:
        reportType = 'المالية';
        break;
      case 3:
        reportType = 'العملاء';
        break;
      default:
        reportType = 'التقرير';
    }

    SnackBarHelper.showInfo(context, 'سيتم تصدير تقرير $reportType قريباً');
  }

  void _printCurrentReport() {
    SnackBarHelper.showInfo(context, 'سيتم طباعة التقرير قريباً');
  }
}
