const String createProductsTable = '''
  CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT DEFAULT '',
    price REAL NOT NULL,
    quantity REAL,
    categoryId INTEGER,
    unitId INTEGER,
    supplierId INTEGER,
    category TEXT,
    unit TEXT,
    purchasePrice REAL,
    salePrice REAL,
    minLevel INTEGER,
    barcode TEXT,
    createdAt TEXT,
    updatedAt TEXT,
    date TEXT,
    retailPrice REAL,
    wholesalePrice REAL,
    warehouseQuantity INTEGER DEFAULT 0,
    storeQuantity INTEGER DEFAULT 0,
    FOREIGN KEY (categoryId) REFERENCES categories(id),
    FOREIGN KEY (unitId) REFERENCES units(id),
    FOREIGN KEY (supplierId) REFERENCES suppliers(id)
  );
''';

const String createCustomersTable = '''
  CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    address TEXT,
    balance REAL DEFAULT 0.0,
    created_at TEXT,
    updated_at TEXT,
    notes TEXT
  );
''';

const String createSuppliersTable = '''
  CREATE TABLE suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    address TEXT
  );
''';

const String createCategoriesTable = '''
  CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT
  );
''';

const String createUnitsTable = '''
  CREATE TABLE units (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    symbol TEXT
  );
''';

const String createSalesTable = '''
  CREATE TABLE sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customerId INTEGER,
    date TEXT,
    total REAL,
    notes TEXT,
    status TEXT,
    invoiceNumber TEXT,
    customerName TEXT,
    paymentMethod TEXT,
    totalWholesaleAmount REAL,
    totalRetailAmount REAL,
    remainingRetailAmount REAL,
    notesForRetailItems TEXT,
    paidAmount REAL DEFAULT 0.0,
    remainingAmount REAL DEFAULT 0.0,
    saleType TEXT,
    createdAt TEXT,
    updatedAt TEXT,
    FOREIGN KEY (customerId) REFERENCES customers(id)
  );
''';

const String createSaleItemsTable = '''
  CREATE TABLE sale_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    saleId INTEGER,
    productId INTEGER,
    quantity REAL,
    price REAL,
    itemType TEXT,
    FOREIGN KEY (saleId) REFERENCES sales(id),
    FOREIGN KEY (productId) REFERENCES products(id)
  );
''';

const String createPurchasesTable = '''
  CREATE TABLE purchases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplierId INTEGER,
    date TEXT,
    total REAL,
    notes TEXT,
    FOREIGN KEY (supplierId) REFERENCES suppliers(id)
  );
''';

const String createPurchaseItemsTable = '''
  CREATE TABLE purchase_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchaseId INTEGER,
    productId INTEGER,
    quantity REAL,
    price REAL,
    FOREIGN KEY (purchaseId) REFERENCES purchases(id),
    FOREIGN KEY (productId) REFERENCES products(id)
  );
''';

const String createExpensesTable = '''
  CREATE TABLE expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    expenseDate TEXT,
    category TEXT,
    amount REAL,
    description TEXT,
    notes TEXT,
    status TEXT DEFAULT 'active'
  );
''';

const String createOrdersTable = '''
  CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customerId INTEGER,
    date TEXT,
    status TEXT,
    total REAL,
    FOREIGN KEY (customerId) REFERENCES customers(id)
  );
''';

const String createOrderItemsTable = '''
  CREATE TABLE order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    orderId INTEGER,
    productId INTEGER,
    quantity REAL,
    price REAL,
    FOREIGN KEY (orderId) REFERENCES orders(id),
    FOREIGN KEY (productId) REFERENCES products(id)
  );
''';

const String createBackupsTable = '''
  CREATE TABLE backups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filePath TEXT,
    date TEXT,
    type TEXT
  );
''';

const String createActivitiesTable = '''
  CREATE TABLE activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT,
    description TEXT,
    date TEXT
  );
''';

const String createTransactionsTable = '''
  CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT,
    description TEXT,
    amount REAL,
    type TEXT
  );
''';

const String createDailySummaryTable = '''
  CREATE TABLE daily_summary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT,
    sales REAL,
    purchases REAL,
    expenses REAL
  );
''';

const String createCustomerStatementTable = '''
  CREATE TABLE customer_statement (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customerId INTEGER,
    date TEXT,
    description TEXT,
    amount REAL,
    balance REAL,
    FOREIGN KEY (customerId) REFERENCES customers(id)
  );
''';

const String createSupplierStatementTable = '''
  CREATE TABLE supplier_statement (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplierId INTEGER,
    date TEXT,
    description TEXT,
    amount REAL,
    balance REAL,
    FOREIGN KEY (supplierId) REFERENCES suppliers(id)
  );
''';

const String createInternalTransfersTable = '''
  CREATE TABLE internal_transfers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transferDate INTEGER,
    productId INTEGER,
    transferredQuantity INTEGER,
    wholesalePriceAtTransfer REAL,
    retailPriceAtTransfer REAL,
    totalWholesaleValue REAL,
    totalRetailValue REAL,
    notes TEXT,
    FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
  );
''';

const String createStoreInventoryAdjustmentsTable = '''
  CREATE TABLE store_inventory_adjustments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    adjustmentDate INTEGER,
    productId INTEGER,
    countedQuantity INTEGER,
    recordedStoreQuantity INTEGER,
    difference INTEGER,
    retailPriceAtAdjustment REAL,
    adjustmentValue REAL,
    notes TEXT,
    FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
  );
''';

/// Performance indexes for frequently queried columns
const List<String> createIndexes = <String>[
  'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
  'CREATE INDEX IF NOT EXISTS idx_products_category ON products(categoryId)',
  'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)',
  'CREATE INDEX IF NOT EXISTS idx_suppliers_email ON suppliers(email)',
  'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date)',
  'CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(date)',
  'CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customerId)',
  'CREATE INDEX IF NOT EXISTS idx_purchases_supplier '
      'ON purchases(supplierId)',
  'CREATE INDEX IF NOT EXISTS idx_internal_transfers_date ON internal_transfers(transferDate)',
  'CREATE INDEX IF NOT EXISTS idx_internal_transfers_product ON internal_transfers(productId)',
  'CREATE INDEX IF NOT EXISTS idx_store_adjustments_date ON store_inventory_adjustments(adjustmentDate)',
  'CREATE INDEX IF NOT EXISTS idx_store_adjustments_product ON store_inventory_adjustments(productId)',
];
