class Product {
  int? id;
  String name;
  String description;
  double price; // Wholesale price
  double? quantity; // Legacy field for backward compatibility
  int? categoryId;
  int? unitId;
  int? supplierId;

  // خصائص إضافية مطلوبة
  String? category;
  String? unit;
  double? purchasePrice;
  double? salePrice;
  int? minLevel;
  String? barcode;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? date;

  // حقول جديدة لنظام المخزن والبقالة
  double? retailPrice; // سعر البيع بالتجزئة
  double? wholesalePrice; // سعر البيع بالجملة
  int? warehouseQuantity; // الكمية المتوفرة في المخزن
  int? storeQuantity; // الكمية المتوفرة في البقالة

  Product({
    this.id,
    required this.name,
    this.description = '',
    required this.price,
    this.quantity,
    this.categoryId,
    this.unitId,
    this.supplierId,
    this.category,
    this.unit,
    this.purchasePrice,
    this.salePrice,
    this.minLevel,
    this.barcode,
    this.createdAt,
    this.updatedAt,
    this.date,
    this.retailPrice,
    this.wholesalePrice,
    this.warehouseQuantity,
    this.storeQuantity,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'quantity': quantity,
      'categoryId': categoryId,
      'unitId': unitId,
      'supplierId': supplierId,
      'category': category,
      'unit': unit,
      'purchasePrice': purchasePrice,
      'salePrice': salePrice,
      'minLevel': minLevel,
      'barcode': barcode,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'date': date,
      'retailPrice': retailPrice,
      'wholesalePrice': wholesalePrice,
      'warehouseQuantity': warehouseQuantity,
      'storeQuantity': storeQuantity,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      price: map['price']?.toDouble(),
      quantity: map['quantity']?.toDouble(),
      categoryId: map['categoryId'],
      unitId: map['unitId'],
      supplierId: map['supplierId'],
      category: map['category'],
      unit: map['unit'],
      purchasePrice: map['purchasePrice']?.toDouble(),
      salePrice: map['salePrice']?.toDouble(),
      minLevel: map['minLevel'],
      barcode: map['barcode'],
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
      date: map['date'],
      retailPrice: map['retailPrice']?.toDouble(),
      wholesalePrice: map['wholesalePrice']?.toDouble(),
      warehouseQuantity: map['warehouseQuantity'],
      storeQuantity: map['storeQuantity'],
    );
  }

  /// إنشاء نسخة جديدة من المنتج مع تحديث بعض الخصائص
  Product copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    double? quantity,
    int? categoryId,
    int? unitId,
    int? supplierId,
    String? category,
    String? unit,
    double? purchasePrice,
    double? salePrice,
    int? minLevel,
    String? barcode,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? date,
    double? retailPrice,
    double? wholesalePrice,
    int? warehouseQuantity,
    int? storeQuantity,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      categoryId: categoryId ?? this.categoryId,
      unitId: unitId ?? this.unitId,
      supplierId: supplierId ?? this.supplierId,
      category: category ?? this.category,
      unit: unit ?? this.unit,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      salePrice: salePrice ?? this.salePrice,
      minLevel: minLevel ?? this.minLevel,
      barcode: barcode ?? this.barcode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      date: date ?? this.date,
      retailPrice: retailPrice ?? this.retailPrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      warehouseQuantity: warehouseQuantity ?? this.warehouseQuantity,
      storeQuantity: storeQuantity ?? this.storeQuantity,
    );
  }
}
