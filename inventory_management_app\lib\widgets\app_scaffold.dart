import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'bottom_navigation.dart';

/// Scaffold مخصص يحتوي على شريط التنقل السفلي
class AppScaffold extends StatelessWidget {
  /// عنوان الشاشة
  final String title;

  /// محتوى الشاشة
  final Widget body;

  /// أزرار شريط التطبيق
  final List<Widget>? actions;

  /// زر الإجراء العائم
  final Widget? floatingActionButton;

  /// هل يجب إظهار سهم الرجوع
  final bool showBackButton;

  /// المسار الحالي للتنقل السفلي
  final String currentRoute;

  const AppScaffold({
    super.key,
    required this.title,
    required this.body,
    required this.currentRoute,
    this.actions,
    this.floatingActionButton,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(title),
          automaticallyImplyLeading: false, // نتحكم يدوياً في زر الرجوع
          leading: showBackButton && _shouldShowBackButton(context)
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: () => _handleBackPress(context),
                  tooltip: 'رجوع',
                )
              : null,
          actions: actions,
        ),
        body: body,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: CustomBottomNavigation(
          currentIndex: BottomNavigationHelper.getCurrentIndex(currentRoute),
        ),
      ),
    );
  }

  /// تحديد ما إذا كان يجب إظهار زر الرجوع
  bool _shouldShowBackButton(BuildContext context) {
    // إظهار زر الرجوع إذا لم نكن في الشاشة الرئيسية
    return currentRoute != '/' &&
        currentRoute != '/dashboard' &&
        context.canPop();
  }

  /// معالجة الضغط على زر الرجوع
  void _handleBackPress(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      // إذا لم نستطع الرجوع، اذهب للشاشة الرئيسية
      context.go('/');
    }
  }
}
