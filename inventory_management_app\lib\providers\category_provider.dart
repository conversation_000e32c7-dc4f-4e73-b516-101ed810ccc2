import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/category.dart';
import 'package:inventory_management_app/services/category_service.dart';

/// Provider class for managing category state and operations
class CategoryProvider extends ChangeNotifier {
  List<Category> _categories = <Category>[];
  final CategoryService _categoryService = CategoryService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of categories
  List<Category> get categories => _categories;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all categories from the database
  Future<void> fetchCategories() async {
    _setLoading(true);
    _clearError();

    try {
      _categories = await _categoryService.getAllCategories();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new category
  Future<void> addCategory(Category category) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.insertCategory(category);
      await fetchCategories();
    } catch (e) {
      _setError('Failed to add category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing category
  Future<void> updateCategory(Category category) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.updateCategory(category);
      await fetchCategories();
    } catch (e) {
      _setError('Failed to update category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a category
  Future<void> deleteCategory(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.deleteCategory(id);
      await fetchCategories();
    } catch (e) {
      _setError('Failed to delete category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search categories by name
  Future<void> searchCategories(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _categories = await _categoryService.searchCategoriesByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
