import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../config/app_constants.dart';
import '../utils/date_helper.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../models/sale.dart';
import '../models/purchase.dart';

/// فئة مساعدة للتصدير والاستيراد
class ExportImportHelper {
  /// تصدير المنتجات إلى CSV
  static Future<String?> exportProductsToCSV(List<Product> products) async {
    try {
      final StringBuffer csv = StringBuffer();

      // إضافة العناوين
      csv.writeln(
          'الرقم,الاسم,الوصف,الفئة,الوحدة,الكمية,سعر الشراء,سعر البيع,الحد الأدنى,الباركود,تاريخ الإنشاء');

      // إضافة البيانات
      for (final Product product in products) {
        csv.writeln(<Object>[
          product.id ?? '',
          _escapeCsvField(product.name ?? ''),
          _escapeCsvField(product.description ?? ''),
          _escapeCsvField(product.category ?? ''),
          _escapeCsvField(product.unit ?? ''),
          product.quantity ?? 0,
          product.purchasePrice ?? 0,
          product.salePrice ?? 0,
          product.minLevel ?? 0,
          _escapeCsvField(product.barcode ?? ''),
          product.createdAt != null
              ? DateHelper.formatDate(product.createdAt!)
              : '',
        ].join(','));
      }

      return await _saveFile('products_${_getTimestamp()}.csv', csv.toString());
    } catch (e) {
      debugPrint('خطأ في تصدير المنتجات: $e');
      return null;
    }
  }

  /// تصدير العملاء إلى CSV
  static Future<String?> exportCustomersToCSV(List<Customer> customers) async {
    try {
      final StringBuffer csv = StringBuffer();

      // إضافة العناوين
      csv.writeln(
          'الرقم,الاسم,الهاتف,البريد الإلكتروني,العنوان,الرصيد,الملاحظات,تاريخ الإنشاء');

      // إضافة البيانات
      for (final Customer customer in customers) {
        csv.writeln(<dynamic>[
          customer.id ?? '',
          _escapeCsvField(customer.name ?? ''),
          _escapeCsvField(customer.phone ?? ''),
          _escapeCsvField(customer.email ?? ''),
          _escapeCsvField(customer.address ?? ''),
          customer.balance ?? 0,
          _escapeCsvField(customer.notes ?? ''),
          customer.createdAt != null
              ? DateHelper.formatDate(customer.createdAt!)
              : '',
        ].join(','));
      }

      return await _saveFile(
          'customers_${_getTimestamp()}.csv', csv.toString());
    } catch (e) {
      debugPrint('خطأ في تصدير العملاء: $e');
      return null;
    }
  }

  /// تصدير الموردين إلى CSV
  static Future<String?> exportSuppliersToCSV(List<Supplier> suppliers) async {
    try {
      final StringBuffer csv = StringBuffer();

      // إضافة العناوين
      csv.writeln(
          'الرقم,الاسم,الهاتف,البريد الإلكتروني,العنوان,الملاحظات,تاريخ الإنشاء');

      // إضافة البيانات
      for (final Supplier supplier in suppliers) {
        csv.writeln(<Object>[
          supplier.id ?? '',
          _escapeCsvField(supplier.name ?? ''),
          _escapeCsvField(supplier.phone ?? ''),
          _escapeCsvField(supplier.email ?? ''),
          _escapeCsvField(supplier.address ?? ''),
          _escapeCsvField(supplier.notes ?? ''),
          supplier.createdAt != null
              ? DateHelper.formatDate(supplier.createdAt!)
              : '',
        ].join(','));
      }

      return await _saveFile(
          'suppliers_${_getTimestamp()}.csv', csv.toString());
    } catch (e) {
      debugPrint('خطأ في تصدير الموردين: $e');
      return null;
    }
  }

  /// تصدير المبيعات إلى CSV
  static Future<String?> exportSalesToCSV(List<Sale> sales) async {
    try {
      final StringBuffer csv = StringBuffer();

      // إضافة العناوين
      csv.writeln(
          'الرقم,رقم الفاتورة,العميل,التاريخ,المبلغ الإجمالي,طريقة الدفع,الحالة,الملاحظات');

      // إضافة البيانات
      for (final Sale sale in sales) {
        csv.writeln(<Object>[
          sale.id ?? '',
          _escapeCsvField(sale.invoiceNumber ?? ''),
          _escapeCsvField(sale.customerName ?? ''),
          sale.date != null ? sale.date! : '',
          sale.total ?? 0,
          _escapeCsvField(sale.paymentMethod ?? ''),
          _escapeCsvField(sale.status ?? ''),
          _escapeCsvField(sale.notes ?? ''),
        ].join(','));
      }

      return await _saveFile('sales_${_getTimestamp()}.csv', csv.toString());
    } catch (e) {
      debugPrint('خطأ في تصدير المبيعات: $e');
      return null;
    }
  }

  /// تصدير المشتريات إلى CSV
  static Future<String?> exportPurchasesToCSV(List<Purchase> purchases) async {
    try {
      final StringBuffer csv = StringBuffer();

      // إضافة العناوين
      csv.writeln(
          'الرقم,رقم الفاتورة,المورد,التاريخ,المبلغ الإجمالي,الحالة,الملاحظات');

      // إضافة البيانات
      for (final Purchase purchase in purchases) {
        csv.writeln(<Object>[
          purchase.id ?? '',
          _escapeCsvField(purchase.invoiceNumber ?? ''),
          _escapeCsvField(purchase.supplierName ?? ''),
          purchase.date != null ? purchase.date! : '',
          purchase.total ?? 0,
          _escapeCsvField(purchase.status ?? ''),
          _escapeCsvField(purchase.notes ?? ''),
        ].join(','));
      }

      return await _saveFile(
          'purchases_${_getTimestamp()}.csv', csv.toString());
    } catch (e) {
      debugPrint('خطأ في تصدير المشتريات: $e');
      return null;
    }
  }

  /// تصدير البيانات إلى JSON
  static Future<String?> exportToJSON({
    List<Product>? products,
    List<Customer>? customers,
    List<Supplier>? suppliers,
    List<Sale>? sales,
    List<Purchase>? purchases,
  }) async {
    try {
      final Map<String, dynamic> data = <String, dynamic>{
        'exportDate': DateTime.now().toIso8601String(),
        'appName': AppConstants.appName,
        'version': AppConstants.appVersion,
      };

      if (products != null) {
        data['products'] = products.map((Product p) => p.toMap()).toList();
      }

      if (customers != null) {
        data['customers'] = customers.map((Customer c) => c.toMap()).toList();
      }

      if (suppliers != null) {
        data['suppliers'] = suppliers.map((Supplier s) => s.toMap()).toList();
      }

      if (sales != null) {
        data['sales'] = sales.map((Sale s) => s.toMap()).toList();
      }

      if (purchases != null) {
        data['purchases'] = purchases.map((Purchase p) => p.toMap()).toList();
      }

      final String jsonString =
          const JsonEncoder.withIndent('  ').convert(data);
      return await _saveFile('export_${_getTimestamp()}.json', jsonString);
    } catch (e) {
      debugPrint('خطأ في تصدير JSON: $e');
      return null;
    }
  }

  /// تصدير تقرير مخصص
  static Future<String?> exportCustomReport({
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String format = 'csv',
  }) async {
    try {
      if (format.toLowerCase() == 'json') {
        final Map<String, Object> reportData = <String, Object>{
          'title': title,
          'exportDate': DateTime.now().toIso8601String(),
          'headers': headers,
          'data': data,
        };

        final String jsonString =
            const JsonEncoder.withIndent('  ').convert(reportData);
        return await _saveFile('report_${_getTimestamp()}.json', jsonString);
      } else {
        // تصدير CSV
        final StringBuffer csv = StringBuffer();

        // إضافة العنوان
        csv.writeln('# $title');
        csv.writeln(
            '# تاريخ التصدير: ${DateHelper.formatDateTime(DateTime.now())}');
        csv.writeln();

        // إضافة العناوين
        csv.writeln(headers.map(_escapeCsvField).join(','));

        // إضافة البيانات
        for (final Map<String, dynamic> row in data) {
          final String values = headers
              .map((String header) =>
                  _escapeCsvField(row[header]?.toString() ?? ''))
              .join(',');
          csv.writeln(values);
        }

        return await _saveFile('report_${_getTimestamp()}.csv', csv.toString());
      }
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }

  /// استيراد المنتجات من CSV
  static Future<List<Product>?> importProductsFromCSV(String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final List<String> lines = content.split('\n');

      if (lines.isEmpty) return null;

      final List<Product> products = <Product>[];

      // تخطي العنوان
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) continue;

        final List<String> fields = _parseCsvLine(line);
        if (fields.length >= 11) {
          final Product product = Product(
            name: fields[1],
            description: fields[2].isEmpty ? '' : fields[2],
            price: double.tryParse(fields[7]) ?? 0.0,
            category: fields[3],
            unit: fields[4],
            quantity: (int.tryParse(fields[5]) ?? 0).toDouble(),
            purchasePrice: double.tryParse(fields[6]) ?? 0.0,
            salePrice: double.tryParse(fields[7]) ?? 0.0,
            minLevel: int.tryParse(fields[8]) ?? 0,
            barcode: fields[9].isEmpty ? null : fields[9],
            createdAt: DateTime.now(),
          );

          products.add(product);
        }
      }

      return products;
    } catch (e) {
      debugPrint('خطأ في استيراد المنتجات: $e');
      return null;
    }
  }

  /// استيراد العملاء من CSV
  static Future<List<Customer>?> importCustomersFromCSV(String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final List<String> lines = content.split('\n');

      if (lines.isEmpty) return null;

      final List<Customer> customers = <Customer>[];

      // تخطي العنوان
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) continue;

        final List<String> fields = _parseCsvLine(line);
        if (fields.length >= 8) {
          final Customer customer = Customer(
            name: fields[1],
            phone: fields[2],
            email: fields[3].isEmpty ? null : fields[3],
            address: fields[4].isEmpty ? null : fields[4],
            balance: double.tryParse(fields[5]) ?? 0,
            createdAt: DateTime.now(),
          );

          customers.add(customer);
        }
      }

      return customers;
    } catch (e) {
      debugPrint('خطأ في استيراد العملاء: $e');
      return null;
    }
  }

  /// استيراد الموردين من CSV
  static Future<List<Supplier>?> importSuppliersFromCSV(String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final List<String> lines = content.split('\n');

      if (lines.isEmpty) return null;

      final List<Supplier> suppliers = <Supplier>[];

      // تخطي العنوان
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) continue;

        final List<String> fields = _parseCsvLine(line);
        if (fields.length >= 7) {
          final Supplier supplier = Supplier(
            name: fields[1],
            phone: fields[2],
            email: fields[3].isEmpty ? null : fields[3],
            address: fields[4].isEmpty ? null : fields[4],
            notes: fields[5].isEmpty ? null : fields[5],
            createdAt: DateTime.now(),
          );

          suppliers.add(supplier);
        }
      }

      return suppliers;
    } catch (e) {
      debugPrint('خطأ في استيراد الموردين: $e');
      return null;
    }
  }

  /// استيراد المبيعات من CSV
  static Future<List<Sale>?> importSalesFromCSV(String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final List<String> lines = content.split('\n');

      if (lines.isEmpty) return null;

      final List<Sale> sales = <Sale>[];

      // تخطي العنوان
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) continue;

        final List<String> fields = _parseCsvLine(line);
        if (fields.length >= 8) {
          final Sale sale = Sale(
            invoiceNumber: fields[1],
            customerName: fields[2],
            date: fields[3],
            total: double.tryParse(fields[4]) ?? 0.0,
            paymentMethod: fields[5],
            status: fields[6],
            notes: fields[7].isEmpty ? null : fields[7],
          );

          sales.add(sale);
        }
      }

      return sales;
    } catch (e) {
      debugPrint('خطأ في استيراد المبيعات: $e');
      return null;
    }
  }

  /// استيراد المشتريات من CSV
  static Future<List<Purchase>?> importPurchasesFromCSV(String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final List<String> lines = content.split('\n');

      if (lines.isEmpty) return null;

      final List<Purchase> purchases = <Purchase>[];

      // تخطي العنوان
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) continue;

        final List<String> fields = _parseCsvLine(line);
        if (fields.length >= 7) {
          final Purchase purchase = Purchase(
            invoiceNumber: fields[1],
            supplierName: fields[2],
            date: fields[3],
            total: double.tryParse(fields[4]) ?? 0.0,
            status: fields[5],
            notes: fields[6].isEmpty ? null : fields[6],
          );

          purchases.add(purchase);
        }
      }

      return purchases;
    } catch (e) {
      debugPrint('خطأ في استيراد المشتريات: $e');
      return null;
    }
  }

  /// استيراد البيانات من JSON
  static Future<Map<String, List<dynamic>>?> importFromJSON(
      String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final Map<String, dynamic> data =
          json.decode(content) as Map<String, dynamic>;

      final Map<String, List> result = <String, List<dynamic>>{};

      if (data.containsKey('products')) {
        final List productsList = data['products'] as List;
        result['products'] =
            productsList.map((p) => Product.fromMap(p)).toList();
      }

      if (data.containsKey('customers')) {
        final List customersList = data['customers'] as List;
        result['customers'] =
            customersList.map((c) => Customer.fromMap(c)).toList();
      }

      if (data.containsKey('suppliers')) {
        final List suppliersList = data['suppliers'] as List;
        result['suppliers'] =
            suppliersList.map((s) => Supplier.fromMap(s)).toList();
      }

      if (data.containsKey('sales')) {
        final List salesList = data['sales'] as List;
        result['sales'] = salesList.map((s) => Sale.fromMap(s)).toList();
      }

      if (data.containsKey('purchases')) {
        final List purchasesList = data['purchases'] as List;
        result['purchases'] =
            purchasesList.map((p) => Purchase.fromMap(p)).toList();
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في استيراد JSON: $e');
      return null;
    }
  }

  /// الحصول على مجلد التصدير
  static Future<String> _getExportDirectory() async {
    if (kIsWeb) {
      return 'exports';
    }

    final Directory appDir = await getApplicationDocumentsDirectory();
    return '${appDir.path}/${AppConstants.exportsFolder}';
  }

  /// حفظ ملف
  static Future<String?> _saveFile(String fileName, String content) async {
    try {
      final String exportDir = await _getExportDirectory();
      final Directory directory = Directory(exportDir);

      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      final File file = File('$exportDir/$fileName');
      await file.writeAsString(content, encoding: utf8);

      return file.path;
    } catch (e) {
      debugPrint('خطأ في حفظ الملف: $e');
      return null;
    }
  }

  /// إنشاء طابع زمني للملف
  static String _getTimestamp() {
    return DateHelper.formatDate(
      DateTime.now(),
      format: 'yyyyMMdd_HHmmss',
    );
  }

  /// تنظيف حقل CSV
  static String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  /// تحليل سطر CSV
  static List<String> _parseCsvLine(String line) {
    final List<String> fields = <String>[];
    final StringBuffer buffer = StringBuffer();
    bool inQuotes = false;

    for (int i = 0; i < line.length; i++) {
      final String char = line[i];

      if (char == '"') {
        if (inQuotes && i + 1 < line.length && line[i + 1] == '"') {
          buffer.write('"');
          i++; // تخطي الاقتباس المزدوج
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char == ',' && !inQuotes) {
        fields.add(buffer.toString());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }

    fields.add(buffer.toString());
    return fields;
  }

  /// الحصول على قائمة الملفات المصدرة
  static Future<List<ExportedFile>> getExportedFiles() async {
    try {
      final String exportDir = await _getExportDirectory();
      final Directory directory = Directory(exportDir);

      if (!await directory.exists()) {
        return <ExportedFile>[];
      }

      final List<File> files = await directory
          .list()
          .where((FileSystemEntity file) => file is File)
          .cast<File>()
          .toList();

      final List<ExportedFile> exportedFiles = <ExportedFile>[];

      for (final File file in files) {
        final FileStat stat = await file.stat();
        final String fileName = path.basename(file.path);

        exportedFiles.add(ExportedFile(
          name: fileName,
          path: file.path,
          size: stat.size,
          createdAt: stat.modified,
          type: _getFileType(fileName),
        ));
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      exportedFiles.sort((ExportedFile a, ExportedFile b) =>
          b.createdAt.compareTo(a.createdAt));

      return exportedFiles;
    } catch (e) {
      debugPrint('خطأ في قراءة الملفات المصدرة: $e');
      return <ExportedFile>[];
    }
  }

  /// حذف ملف مصدر
  static Future<bool> deleteExportedFile(String filePath) async {
    try {
      final File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف الملف: $e');
      return false;
    }
  }

  /// الحصول على نوع الملف
  static String _getFileType(String fileName) {
    final String extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'csv':
        return 'CSV';
      case 'json':
        return 'JSON';
      case 'pdf':
        return 'PDF';
      case 'xlsx':
        return 'Excel';
      default:
        return 'Unknown';
    }
  }
}

/// معلومات الملف المصدر
class ExportedFile {
  final String name;
  final String path;
  final int size;
  final DateTime createdAt;
  final String type;

  ExportedFile({
    required this.name,
    required this.path,
    required this.size,
    required this.createdAt,
    required this.type,
  });

  String get formattedSize {
    if (size < 1024) {
      return '$size بايت';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }

  String get formattedDate {
    return DateHelper.formatDateTime(createdAt);
  }
}
