import 'package:flutter/material.dart';
import '../config/app_colors.dart';
import '../config/app_styles.dart';
import '../config/app_dimensions.dart';
import 'loading_indicator_widget.dart';

/// 🎯 زر مخصص موحد لأسامة ماركت
class CustomElevatedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? width;
  final double? height;
  final bool isLoading;
  final bool isFullWidth;
  final ButtonSize size;
  final ButtonVariant variant;

  const CustomElevatedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.width,
    this.height,
    this.isLoading = false,
    this.isFullWidth = false,
    this.size = ButtonSize.medium,
    this.variant = ButtonVariant.primary,
  });

  @override
  Widget build(BuildContext context) {
    final ButtonStyle buttonStyle = _getButtonStyle();
    final double buttonHeight = height ?? _getHeightForSize();
    final double? buttonWidth = isFullWidth ? double.infinity : width;

    Widget buttonChild = _buildButtonContent();

    if (icon != null && !isLoading) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            icon,
            size: _getIconSizeForSize(),
          ),
          const SizedBox(width: 8),
          Flexible(child: buttonChild),
        ],
      );
    }

    return SizedBox(
      width: buttonWidth,
      height: buttonHeight,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: buttonChild,
      ),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SmallLoadingIndicator(
            color: _getForegroundColor(),
            size: _getIconSizeForSize(),
          ),
          const SizedBox(width: 8),
          Text(
            'جاري التحميل...',
            style: _getTextStyleForSize(),
          ),
        ],
      );
    }

    return Text(
      text,
      style: _getTextStyleForSize(),
      textAlign: TextAlign.center,
    );
  }

  ButtonStyle _getButtonStyle() {
    final Color bgColor = _getBackgroundColor();
    final Color fgColor = _getForegroundColor();
    final BorderRadius borderRadius = _getBorderRadius();

    switch (variant) {
      case ButtonVariant.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: bgColor,
          foregroundColor: fgColor,
          elevation: AppDimensions.elevationM,
          shadowColor: bgColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
          ),
          padding: _getPadding(),
        );

      case ButtonVariant.secondary:
        return OutlinedButton.styleFrom(
          foregroundColor: bgColor,
          side: BorderSide(color: bgColor, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
          ),
          padding: _getPadding(),
        );

      case ButtonVariant.text:
        return TextButton.styleFrom(
          foregroundColor: bgColor,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
          ),
          padding: _getPadding(),
        );

      case ButtonVariant.accent:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.accent,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppDimensions.elevationM,
          shadowColor: AppColors.accent.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
          ),
          padding: _getPadding(),
        );
    }
  }

  Color _getBackgroundColor() {
    if (backgroundColor != null) return backgroundColor!;

    switch (variant) {
      case ButtonVariant.primary:
        return AppColors.primary;
      case ButtonVariant.accent:
        return AppColors.accent;
      case ButtonVariant.secondary:
      case ButtonVariant.text:
        return AppColors.primary;
    }
  }

  Color _getForegroundColor() {
    if (foregroundColor != null) return foregroundColor!;

    switch (variant) {
      case ButtonVariant.primary:
      case ButtonVariant.accent:
        return AppColors.textOnPrimary;
      case ButtonVariant.secondary:
      case ButtonVariant.text:
        return AppColors.primary;
    }
  }

  BorderRadius _getBorderRadius() {
    switch (size) {
      case ButtonSize.small:
        return BorderRadius.circular(AppDimensions.radiusS);
      case ButtonSize.medium:
        return BorderRadius.circular(AppDimensions.radiusM);
      case ButtonSize.large:
        return BorderRadius.circular(AppDimensions.radiusL);
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingXL,
          vertical: AppDimensions.paddingL,
        );
    }
  }

  double _getHeightForSize() {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.buttonHeightSmall;
      case ButtonSize.medium:
        return AppDimensions.buttonHeight;
      case ButtonSize.large:
        return AppDimensions.buttonHeightLarge;
    }
  }

  double _getIconSizeForSize() {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.iconS;
      case ButtonSize.medium:
        return AppDimensions.iconM;
      case ButtonSize.large:
        return AppDimensions.iconL;
    }
  }

  TextStyle _getTextStyleForSize() {
    final TextStyle baseStyle = AppStyles.buttonTextStyle.copyWith(
      color: _getForegroundColor(),
    );

    switch (size) {
      case ButtonSize.small:
        return baseStyle.copyWith(fontSize: 14);
      case ButtonSize.medium:
        return baseStyle.copyWith(fontSize: 16);
      case ButtonSize.large:
        return baseStyle.copyWith(fontSize: 18);
    }
  }
}

/// أحجام الأزرار
enum ButtonSize {
  small,
  medium,
  large,
}

/// أنواع الأزرار
enum ButtonVariant {
  primary,
  secondary,
  accent,
  text,
}

/// زر إجراء سريع للوحة التحكم
class QuickActionButton extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final bool isLoading;

  const QuickActionButton({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: AppDimensions.elevationS,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: InkWell(
        onTap: isLoading ? null : onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Container(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          height: AppDimensions.dashboardCardHeight,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              if (isLoading)
                const SmallLoadingIndicator()
              else
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: AppDimensions.iconL,
                  ),
                ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                title,
                style: AppStyles.titleSmall.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// زر فلوتنج مخصص
class CustomFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final bool isExtended;
  final String? label;

  const CustomFloatingActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.isExtended = false,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    if (isExtended && label != null) {
      return FloatingActionButton.extended(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(
          label!,
          style: AppStyles.buttonTextStyle,
        ),
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppDimensions.elevationL,
        tooltip: tooltip,
      );
    }

    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      elevation: AppDimensions.elevationL,
      tooltip: tooltip,
      child: Icon(icon),
    );
  }
}
