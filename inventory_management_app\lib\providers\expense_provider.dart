import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/expense.dart';
import 'package:inventory_management_app/services/expense_service.dart';

/// Provider class for managing expense state and operations
class ExpenseProvider extends ChangeNotifier {
  List<Expense> _expenses = <Expense>[];
  List<Expense> _filteredExpenses = <Expense>[];
  final ExpenseService _expenseService = ExpenseService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of expenses
  List<Expense> get expenses => _expenses;

  /// Get the filtered list of expenses
  List<Expense> get filteredExpenses =>
      _filteredExpenses.isEmpty ? _expenses : _filteredExpenses;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all expenses from the database
  Future<void> fetchExpenses() async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _expenseService.getAllExpenses();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch expenses: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new expense
  Future<void> addExpense(Expense expense) async {
    _setLoading(true);
    _clearError();

    try {
      await _expenseService.insertExpense(expense);
      await fetchExpenses();
    } catch (e) {
      _setError('Failed to add expense: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing expense
  Future<void> updateExpense(Expense expense) async {
    _setLoading(true);
    _clearError();

    try {
      await _expenseService.updateExpense(expense);
      await fetchExpenses();
    } catch (e) {
      _setError('Failed to update expense: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an expense
  Future<void> deleteExpense(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _expenseService.deleteExpense(id);
      await fetchExpenses();
    } catch (e) {
      _setError('Failed to delete expense: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get expenses by category
  Future<void> getExpensesByCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _expenseService.getExpensesByCategory(categoryId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get expenses by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get expenses by date range
  Future<void> getExpensesByDateRange(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      _expenses =
          await _expenseService.getExpensesByDateRange(startDate, endDate);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get expenses by date range: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search expenses by notes
  Future<void> searchExpensesByNotes(String notes) async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _expenseService.searchExpensesByNotes(notes);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search expenses by notes: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get total expenses amount
  Future<double> getTotalExpensesAmount() async {
    try {
      return await _expenseService.getTotalExpensesAmount();
    } catch (e) {
      _setError('Failed to get total expenses amount: $e');
      return 0.0;
    }
  }

  /// Filter expenses based on search query, category, and date range
  void filterExpenses({
    String? searchQuery,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    List<Expense> filtered = List.from(_expenses);

    // Filter by search query (description or amount)
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filtered = filtered.where((Expense expense) {
        return (expense.description
                    ?.toLowerCase()
                    .contains(searchQuery.toLowerCase()) ??
                false) ||
            expense.formattedAmount.contains(searchQuery);
      }).toList();
    }

    // Filter by category
    if (category != null && category.isNotEmpty && category != 'الكل') {
      filtered = filtered
          .where((Expense expense) => expense.category == category)
          .toList();
    }

    // Filter by date range
    if (startDate != null && endDate != null) {
      filtered = filtered.where((Expense expense) {
        if (expense.expenseDate == null) return false;
        return expense.expenseDate!
                .isAfter(startDate.subtract(const Duration(days: 1))) &&
            expense.expenseDate!.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    _filteredExpenses = filtered;
    notifyListeners();
  }

  /// Clear filters and show all expenses
  void clearFilters() {
    _filteredExpenses = <Expense>[];
    notifyListeners();
  }

  /// Get expenses by period (today, week, month, year)
  void filterByPeriod(String period) {
    final DateTime now = DateTime.now();
    DateTime startDate;
    DateTime endDate = now;

    switch (period) {
      case 'اليوم':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'الأسبوع':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'الشهر':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'السنة':
        startDate = DateTime(now.year, 1, 1);
        break;
      default:
        clearFilters();
        return;
    }

    filterExpenses(startDate: startDate, endDate: endDate);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
