/// نظام التوجيه الموحد لتطبيق أسامة ماركت
///
/// يحتوي على جميع مسارات التنقل في التطبيق باستخدام GoRouter
///
/// المسارات المتاحة:
/// - /splash - شاشة البداية
/// - / - الشاشة الرئيسية (Dashboard)
/// - /products - إدارة المنتجات
/// - /customers - إدارة العملاء
/// - /suppliers - إدارة الموردين
/// - /sales - إدارة المبيعات
/// - /purchases - إدارة المشتريات
/// - /orders - إدارة الطلبات
/// - /expenses - إدارة المصروفات
/// - /categories - إدارة الفئات
/// - /units - إدارة الوحدات
/// - /reports - التقارير
/// - /settings - الإعدادات
/// - /invoices/* - الفواتير
/// - /statements/* - الكشوفات
/// - /inventory/* - إدارة المخزون
/// - /analytics - التحليلات
/// - /activities - سجل الأنشطة
/// - /onboarding - شاشة التعريف بالتطبيق

import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

// شاشات التطبيق الرئيسية
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/splash/splash_screen.dart';

// شاشات المنتجات
import '../screens/products/product_list_screen.dart';
import '../screens/products/product_form_screen.dart';
import '../screens/products/product_details_screen.dart';

// شاشات العملاء
import '../screens/customers/customer_list_screen.dart';
import '../screens/customers/customer_form_screen.dart';

// شاشات الموردين
import '../screens/suppliers/suppliers_screen.dart';

// شاشات الطلبات
import '../screens/orders/orders_screen.dart';

// شاشات المبيعات
import '../screens/sales/sale_list_screen.dart';

// شاشات المشتريات
import '../screens/purchases/purchases_screen.dart';

// شاشات المصروفات
import '../screens/expenses/expenses_screen.dart';

// شاشات الفئات والوحدات
import '../screens/categories/categories_screen.dart';
import '../screens/units/units_screen.dart';

// شاشات التقارير والإعدادات
import '../screens/reports/reports_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/settings/backup_settings_screen.dart';

// شاشات الفواتير
import '../screens/invoices/sale_invoice_list_screen.dart';
import '../screens/invoices/purchase_invoice_list_screen.dart';

// شاشات الكشوفات
import '../screens/statements/customer_statement_screen.dart';
import '../screens/statements/supplier_statement_screen.dart';

// شاشات المخزون
import '../screens/inventory/internal_transfer_screen.dart';
import '../screens/inventory/store_inventory_adjustment_screen.dart';

// شاشات أخرى
import '../screens/analytics/analytics_screen.dart';
import '../screens/activity/activity_list_screen.dart';
import '../screens/onboarding/onboarding_screen.dart';

/// فئة إدارة التوجيه في التطبيق
/// تحتوي على جميع المسارات والشاشات المتاحة
class AppRouter {
  /// كائن GoRouter الرئيسي للتطبيق
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    routes: <RouteBase>[
      GoRoute(
        path: '/splash',
        builder: (BuildContext context, GoRouterState state) =>
            const SplashScreen(),
      ),
      GoRoute(
        path: '/',
        builder: (BuildContext context, GoRouterState state) =>
            const DashboardScreen(),
      ),
      GoRoute(
        path: '/products',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductListScreen(),
      ),
      GoRoute(
        path: '/products/add',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductFormScreen(),
      ),
      GoRoute(
        path: '/products/edit/:id',
        builder: (BuildContext context, GoRouterState state) {
          final String? productId = state.pathParameters['id'];
          return ProductFormScreen(
            productId: productId,
            isEditing: true,
          );
        },
      ),
      GoRoute(
        path: '/products/details/:id',
        builder: (BuildContext context, GoRouterState state) {
          final String productId = state.pathParameters['id'] ?? '';
          return ProductDetailsScreen(
            productId: productId,
          );
        },
      ),
      GoRoute(
        path: '/customers',
        builder: (BuildContext context, GoRouterState state) =>
            const CustomerListScreen(),
      ),
      GoRoute(
        path: '/customers/add',
        builder: (BuildContext context, GoRouterState state) =>
            const CustomerFormScreen(),
      ),
      GoRoute(
        path: '/customers/edit/:id',
        builder: (BuildContext context, GoRouterState state) {
          final String? customerId = state.pathParameters['id'];
          return CustomerFormScreen(
            customerId: customerId,
            isEditing: true,
          );
        },
      ),
      GoRoute(
        path: '/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            const SuppliersScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (BuildContext context, GoRouterState state) =>
            const OrdersScreen(),
      ),
      GoRoute(
        path: '/sales',
        builder: (BuildContext context, GoRouterState state) =>
            const SaleListScreen(),
      ),
      GoRoute(
        path: '/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            const PurchasesScreen(),
      ),
      GoRoute(
        path: '/expenses',
        builder: (BuildContext context, GoRouterState state) =>
            const ExpensesScreen(),
      ),
      GoRoute(
        path: '/categories',
        builder: (BuildContext context, GoRouterState state) =>
            const CategoriesScreen(),
      ),
      GoRoute(
        path: '/units',
        builder: (BuildContext context, GoRouterState state) =>
            const UnitsScreen(),
      ),
      GoRoute(
        path: '/backup',
        builder: (BuildContext context, GoRouterState state) =>
            const BackupSettingsScreen(),
      ),
      GoRoute(
        path: '/reports',
        builder: (BuildContext context, GoRouterState state) =>
            const ReportsScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (BuildContext context, GoRouterState state) =>
            const SettingsScreen(),
      ),
      GoRoute(
        path: '/settings/backup',
        builder: (BuildContext context, GoRouterState state) =>
            const BackupSettingsScreen(),
      ),
      GoRoute(
        path: '/invoices/sales',
        builder: (BuildContext context, GoRouterState state) =>
            const SaleInvoiceListScreen(),
      ),
      GoRoute(
        path: '/invoices/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            const PurchaseInvoiceListScreen(),
      ),
      GoRoute(
        path: '/statements/customers',
        builder: (BuildContext context, GoRouterState state) =>
            const CustomerStatementScreen(),
      ),
      GoRoute(
        path: '/statements/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            const SupplierStatementScreen(),
      ),
      GoRoute(
        path: '/inventory/transfer',
        builder: (BuildContext context, GoRouterState state) =>
            const InternalTransferScreen(),
      ),
      GoRoute(
        path: '/inventory/adjustment',
        builder: (BuildContext context, GoRouterState state) =>
            const StoreInventoryAdjustmentScreen(),
      ),
      GoRoute(
        path: '/analytics',
        builder: (BuildContext context, GoRouterState state) =>
            const AnalyticsScreen(),
      ),
      GoRoute(
        path: '/activities',
        builder: (BuildContext context, GoRouterState state) =>
            const ActivityListScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        builder: (BuildContext context, GoRouterState state) =>
            const OnboardingScreen(),
      ),
    ],
  );
}
