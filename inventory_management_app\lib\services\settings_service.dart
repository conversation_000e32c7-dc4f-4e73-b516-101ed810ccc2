import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

/// Service for managing app settings using SharedPreferences
class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  SharedPreferences? _prefs;

  /// Initialize the settings service
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Ensure preferences are initialized
  Future<SharedPreferences> get _preferences async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  // Settings Keys
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyLanguage = 'language';
  static const String _keyCurrency = 'currency';
  static const String _keyAutoBackup = 'auto_backup';
  static const String _keyBackupFrequency = 'backup_frequency';
  static const String _keyNotifications = 'notifications';
  static const String _keyLowStockAlert = 'low_stock_alert';
  static const String _keyLowStockThreshold = 'low_stock_threshold';
  static const String _keyCompanyName = 'company_name';
  static const String _keyCompanyAddress = 'company_address';
  static const String _keyCompanyPhone = 'company_phone';
  static const String _keyCompanyEmail = 'company_email';
  static const String _keyTaxRate = 'tax_rate';
  static const String _keyDecimalPlaces = 'decimal_places';

  // Theme Settings
  /// Get current theme mode
  Future<ThemeMode> getThemeMode() async {
    final SharedPreferences prefs = await _preferences;
    final String? themeModeString = prefs.getString(_keyThemeMode);

    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  /// Set theme mode
  Future<bool> setThemeMode(ThemeMode themeMode) async {
    final SharedPreferences prefs = await _preferences;
    String themeModeString;

    switch (themeMode) {
      case ThemeMode.light:
        themeModeString = 'light';
        break;
      case ThemeMode.dark:
        themeModeString = 'dark';
        break;
      case ThemeMode.system:
        themeModeString = 'system';
        break;
    }

    return await prefs.setString(_keyThemeMode, themeModeString);
  }

  // Language Settings
  /// Get current language
  Future<String> getLanguage() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getString(_keyLanguage) ?? 'en';
  }

  /// Set language
  Future<bool> setLanguage(String languageCode) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setString(_keyLanguage, languageCode);
  }

  // Currency Settings
  /// Get current currency
  Future<String> getCurrency() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getString(_keyCurrency) ?? 'USD';
  }

  /// Set currency
  Future<bool> setCurrency(String currency) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setString(_keyCurrency, currency);
  }

  // Backup Settings
  /// Get auto backup enabled status
  Future<bool> getAutoBackupEnabled() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getBool(_keyAutoBackup) ?? false;
  }

  /// Set auto backup enabled status
  Future<bool> setAutoBackupEnabled(bool enabled) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setBool(_keyAutoBackup, enabled);
  }

  /// Get backup frequency in days
  Future<int> getBackupFrequency() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getInt(_keyBackupFrequency) ?? 7; // Default: weekly
  }

  /// Set backup frequency in days
  Future<bool> setBackupFrequency(int days) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setInt(_keyBackupFrequency, days);
  }

  // Notification Settings
  /// Get notifications enabled status
  Future<bool> getNotificationsEnabled() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getBool(_keyNotifications) ?? true;
  }

  /// Set notifications enabled status
  Future<bool> setNotificationsEnabled(bool enabled) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setBool(_keyNotifications, enabled);
  }

  /// Get low stock alert enabled status
  Future<bool> getLowStockAlertEnabled() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getBool(_keyLowStockAlert) ?? true;
  }

  /// Set low stock alert enabled status
  Future<bool> setLowStockAlertEnabled(bool enabled) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setBool(_keyLowStockAlert, enabled);
  }

  /// Get low stock threshold
  Future<double> getLowStockThreshold() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getDouble(_keyLowStockThreshold) ?? 10.0;
  }

  /// Set low stock threshold
  Future<bool> setLowStockThreshold(double threshold) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setDouble(_keyLowStockThreshold, threshold);
  }

  // Company Settings
  /// Get company name
  Future<String> getCompanyName() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getString(_keyCompanyName) ?? 'My Company';
  }

  /// Set company name
  Future<bool> setCompanyName(String name) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setString(_keyCompanyName, name);
  }

  /// Get company address
  Future<String> getCompanyAddress() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getString(_keyCompanyAddress) ?? '';
  }

  /// Set company address
  Future<bool> setCompanyAddress(String address) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setString(_keyCompanyAddress, address);
  }

  /// Get company phone
  Future<String> getCompanyPhone() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getString(_keyCompanyPhone) ?? '';
  }

  /// Set company phone
  Future<bool> setCompanyPhone(String phone) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setString(_keyCompanyPhone, phone);
  }

  /// Get company email
  Future<String> getCompanyEmail() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getString(_keyCompanyEmail) ?? '';
  }

  /// Set company email
  Future<bool> setCompanyEmail(String email) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setString(_keyCompanyEmail, email);
  }

  // Financial Settings
  /// Get tax rate
  Future<double> getTaxRate() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getDouble(_keyTaxRate) ?? 0.0;
  }

  /// Set tax rate
  Future<bool> setTaxRate(double rate) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setDouble(_keyTaxRate, rate);
  }

  /// Get decimal places for currency display
  Future<int> getDecimalPlaces() async {
    final SharedPreferences prefs = await _preferences;
    return prefs.getInt(_keyDecimalPlaces) ?? 2;
  }

  /// Set decimal places for currency display
  Future<bool> setDecimalPlaces(int places) async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.setInt(_keyDecimalPlaces, places);
  }

  // Utility Methods
  /// Reset all settings to default
  Future<bool> resetAllSettings() async {
    final SharedPreferences prefs = await _preferences;
    return await prefs.clear();
  }

  /// Get all settings as a map
  Future<Map<String, dynamic>> getAllSettings() async {
    return <String, dynamic>{
      'theme_mode': await getThemeMode(),
      'language': await getLanguage(),
      'currency': await getCurrency(),
      'auto_backup': await getAutoBackupEnabled(),
      'backup_frequency': await getBackupFrequency(),
      'notifications': await getNotificationsEnabled(),
      'low_stock_alert': await getLowStockAlertEnabled(),
      'low_stock_threshold': await getLowStockThreshold(),
      'company_name': await getCompanyName(),
      'company_address': await getCompanyAddress(),
      'company_phone': await getCompanyPhone(),
      'company_email': await getCompanyEmail(),
      'tax_rate': await getTaxRate(),
      'decimal_places': await getDecimalPlaces(),
    };
  }

  /// Export settings to JSON string
  Future<String> exportSettings() async {
    final Map<String, dynamic> settings = await getAllSettings();
    return settings.toString();
  }

  /// Check if this is the first app launch
  Future<bool> isFirstLaunch() async {
    final SharedPreferences prefs = await _preferences;
    final bool isFirst = prefs.getBool('first_launch') ?? true;
    if (isFirst) {
      await prefs.setBool('first_launch', false);
    }
    return isFirst;
  }
}
