import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/category.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/models/sale_item.dart';
import 'package:inventory_management_app/services/business_logic_service.dart';
import 'package:inventory_management_app/services/transaction_service.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/category_service.dart';
import 'package:inventory_management_app/providers/product_provider.dart';

void main() {
  // Initialize FFI
  sqfliteFfiInit();

  group('Integration Tests', () {
    late BusinessLogicService businessLogic;
    late TransactionService transactionService;
    late ProductService productService;
    late CategoryService categoryService;
    late ProductProvider productProvider;

    setUpAll(() {
      // Use the ffi factory for testing
      databaseFactory = databaseFactoryFfi;
      businessLogic = BusinessLogicService();
      transactionService = TransactionService();
      productService = ProductService();
      categoryService = CategoryService();
      productProvider = ProductProvider();
    });

    group('Business Logic Tests', () {
      test('should validate product name uniqueness', () async {
        // Create a category
        final Category category =
            Category(name: 'Electronics', description: 'Electronic devices');
        final int categoryId = await categoryService.insertCategory(category);

        // Create a product
        final Product product = Product(
          name: 'iPhone',
          description: 'Apple smartphone',
          categoryId: categoryId,
          price: 999.0,
          quantity: 10.0,
        );
        await productService.insertProduct(product);

        // Test uniqueness validation
        final bool isUnique = await businessLogic.isProductNameUniqueInCategory(
            'iPhone', categoryId);
        expect(isUnique, isFalse);

        final bool isUniqueNew = await businessLogic
            .isProductNameUniqueInCategory('Samsung Galaxy', categoryId);
        expect(isUniqueNew, isTrue);
      });

      test('should check deletion constraints', () async {
        // Create category with product
        final Category category = Category(
            name: 'Test Category', description: 'Test category description');
        final int categoryId = await categoryService.insertCategory(category);

        final Product product = Product(
          name: 'Test Product',
          description: 'Test product description',
          categoryId: categoryId,
          price: 100.0,
          quantity: 5.0,
        );
        await productService.insertProduct(product);

        // Should not be able to delete category with products
        final bool canDelete =
            await businessLogic.canDeleteCategory(categoryId);
        expect(canDelete, isFalse);
      });
    });

    group('Transaction Tests', () {
      test('should create sale with items and update stock', () async {
        // Create product
        final Product product = Product(
          name: 'Test Product for Sale',
          description: 'Product for sale testing',
          price: 50.0,
          quantity: 20.0,
        );
        final int productId = await productService.insertProduct(product);

        // Create sale with items
        final Sale sale = Sale(
          customerId: 1,
          date: DateTime.now().toIso8601String(),
          total: 100.0,
        );

        final List<SaleItem> saleItems = <SaleItem>[
          SaleItem(
            productId: productId,
            quantity: 2.0,
            price: 50.0,
          ),
        ];

        // Execute transaction
        final int saleId =
            await transactionService.createSaleWithItems(sale, saleItems);
        expect(saleId, isPositive);

        // Verify stock was updated
        final Product? updatedProduct =
            await productService.getProductById(productId);
        expect(updatedProduct?.quantity, equals(18.0));
      });

      test('should handle insufficient stock error', () async {
        // Create product with low stock
        final Product product = Product(
          name: 'Low Stock Product',
          description: 'Product with low stock',
          price: 30.0,
          quantity: 1.0,
        );
        final int productId = await productService.insertProduct(product);

        // Try to sell more than available
        final Sale sale = Sale(
          customerId: 1,
          date: DateTime.now().toIso8601String(),
          total: 60.0,
        );

        final List<SaleItem> saleItems = <SaleItem>[
          SaleItem(
            productId: productId,
            quantity: 5.0, // More than available
            price: 30.0,
          ),
        ];

        // Should throw exception
        expect(
          () => transactionService.createSaleWithItems(sale, saleItems),
          throwsException,
        );
      });
    });

    group('Provider Integration Tests', () {
      test('should handle provider operations with error handling', () async {
        // Test product provider operations
        await productProvider.fetchProducts();
        expect(productProvider.isLoading, isFalse);
        expect(productProvider.error, isNull);

        // Test adding product
        final Product newProduct = Product(
          name: 'Provider Test Product',
          description: 'Product for provider testing',
          price: 25.0,
          quantity: 15.0,
        );

        await productProvider.addProduct(newProduct);
        expect(productProvider.error, isNull);

        // Test validation
        final bool isValid = await productProvider.validateProductName(
            'Provider Test Product', null);
        expect(isValid, isFalse); // Should be false as it already exists
      });

      test('should handle provider error scenarios', () async {
        // Test error handling by trying to delete non-existent product
        await productProvider.deleteProduct(99999);
        // Should not crash, error should be handled gracefully
        expect(productProvider.error, isNull); // Delete of non-existent is OK
      });
    });

    group('Performance Tests', () {
      test('should handle bulk operations efficiently', () async {
        final Stopwatch stopwatch = Stopwatch()..start();

        // Create multiple products
        for (int i = 0; i < 100; i++) {
          final Product product = Product(
            name: 'Bulk Product $i',
            description: 'Bulk product description $i',
            price: 10.0 + i,
            quantity: 100.0,
          );
          await productService.insertProduct(product);
        }

        stopwatch.stop();
        // print('Bulk insert time: ${stopwatch.elapsedMilliseconds}ms'); // Commented for production

        // Should complete within reasonable time (adjust as needed)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));

        // Verify all products were created
        final List<Product> products = await productService.getAllProducts();
        expect(products.length, greaterThanOrEqualTo(100));
      });
    });
  });
}
