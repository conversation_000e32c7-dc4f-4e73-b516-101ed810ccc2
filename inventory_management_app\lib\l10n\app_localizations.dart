import 'package:flutter/material.dart';

/// تعريب التطبيق - النصوص العربية
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar', 'SA'), // العربية السعودية
    Locale('en', 'US'), // الإنجليزية
  ];

  // العناوين الرئيسية
  String get appTitle => 'إدارة محل المواد الغذائية';
  String get dashboard => 'لوحة التحكم';
  String get products => 'الأصناف الغذائية';
  String get customers => 'العملاء';
  String get suppliers => 'الموردين';
  String get orders => 'الطلبات';
  String get sales => 'المبيعات';
  String get purchases => 'المشتريات';
  String get expenses => 'المصروفات';
  String get categories => 'الفئات';
  String get units => 'الوحدات';
  String get reports => 'التقارير';
  String get backup => 'النسخ الاحتياطي';
  String get settings => 'الإعدادات';

  // الأصناف الغذائية
  String get foodItem => 'صنف غذائي';
  String get foodItems => 'الأصناف الغذائية';
  String get addFoodItem => 'إضافة صنف غذائي';
  String get editFoodItem => 'تعديل صنف غذائي';
  String get deleteFoodItem => 'حذف صنف غذائي';
  String get itemName => 'اسم الصنف';
  String get itemDescription => 'وصف الصنف';
  String get itemPrice => 'السعر';
  String get itemQuantity => 'الكمية';
  String get itemUnit => 'الوحدة';
  String get itemCategory => 'الفئة';
  String get itemSupplier => 'المورد';
  String get expiryDate => 'تاريخ الصلاحية';
  String get barcode => 'الباركود';

  // الفئات الغذائية
  String get dairyProducts => 'منتجات الألبان';
  String get beverages => 'المشروبات';
  String get cannedFood => 'المعلبات';
  String get sweets => 'الحلويات';
  String get cleaningProducts => 'منتجات التنظيف';
  String get frozenFood => 'المجمدات';
  String get bakery => 'المخبوزات';
  String get spices => 'البهارات';
  String get oils => 'الزيوت';
  String get grains => 'الحبوب';

  // الوحدات
  String get piece => 'قطعة';
  String get box => 'علبة';
  String get carton => 'كرتونة';
  String get kilogram => 'كيلو';
  String get gram => 'جرام';
  String get liter => 'لتر';
  String get bottle => 'زجاجة';
  String get pack => 'عبوة';

  // العمليات
  String get add => 'إضافة';
  String get edit => 'تعديل';
  String get delete => 'حذف';
  String get save => 'حفظ';
  String get cancel => 'إلغاء';
  String get confirm => 'تأكيد';
  String get search => 'بحث';
  String get filter => 'تصفية';
  String get sort => 'ترتيب';
  String get refresh => 'تحديث';
  String get export => 'تصدير';
  String get import => 'استيراد';

  // الرسائل
  String get success => 'نجح';
  String get error => 'خطأ';
  String get warning => 'تحذير';
  String get info => 'معلومات';
  String get loading => 'جاري التحميل...';
  String get noData => 'لا توجد بيانات';
  String get noItems => 'لا توجد أصناف';
  String get noCustomers => 'لا يوجد عملاء';
  String get noSuppliers => 'لا يوجد موردين';

  // رسائل التأكيد
  String get deleteConfirmation => 'هل أنت متأكد من الحذف؟';
  String get saveConfirmation => 'هل تريد حفظ التغييرات؟';
  String get exitConfirmation => 'هل تريد الخروج؟';

  // رسائل النجاح
  String get itemAdded => 'تم إضافة الصنف بنجاح';
  String get itemUpdated => 'تم تحديث الصنف بنجاح';
  String get itemDeleted => 'تم حذف الصنف بنجاح';
  String get customerAdded => 'تم إضافة العميل بنجاح';
  String get supplierAdded => 'تم إضافة المورد بنجاح';

  // رسائل الخطأ
  String get itemNotFound => 'الصنف غير موجود';
  String get invalidData => 'بيانات غير صحيحة';
  String get networkError => 'خطأ في الشبكة';
  String get databaseError => 'خطأ في قاعدة البيانات';

  // التقارير
  String get dailySales => 'المبيعات اليومية';
  String get monthlySales => 'المبيعات الشهرية';
  String get inventoryReport => 'تقرير المخزون';
  String get lowStockItems => 'الأصناف منخفضة المخزون';
  String get expiredItems => 'الأصناف منتهية الصلاحية';
  String get topSellingItems => 'الأصناف الأكثر مبيعاً';

  // الإعدادات
  String get appearance => 'المظهر';
  String get language => 'اللغة';
  String get currency => 'العملة';
  String get notifications => 'الإشعارات';
  String get backupSettings => 'إعدادات النسخ الاحتياطي';
  String get about => 'حول التطبيق';

  // العملة
  String get sar => 'ريال سعودي';
  String get aed => 'درهم إماراتي';
  String get usd => 'دولار أمريكي';
  String get eur => 'يورو';

  // التواريخ والأوقات
  String get today => 'اليوم';
  String get yesterday => 'أمس';
  String get thisWeek => 'هذا الأسبوع';
  String get thisMonth => 'هذا الشهر';
  String get thisYear => 'هذا العام';

  // الحالات
  String get active => 'نشط';
  String get inactive => 'غير نشط';
  String get available => 'متوفر';
  String get outOfStock => 'نفد المخزون';
  String get lowStock => 'مخزون منخفض';
  String get expired => 'منتهي الصلاحية';

  // أرقام عربية
  String formatNumber(num number) {
    return number.toString().replaceAllMapped(
          RegExp(r'\d'),
          (Match match) => '٠١٢٣٤٥٦٧٨٩'[int.parse(match.group(0)!)],
        );
  }

  // تنسيق العملة
  String formatCurrency(double amount) {
    return '${formatNumber(amount)} ر.س';
  }
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return <String>['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
