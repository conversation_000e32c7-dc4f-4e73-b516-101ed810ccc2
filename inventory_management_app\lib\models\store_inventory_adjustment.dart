/// Model class representing a store inventory adjustment/count
class StoreInventoryAdjustment {
  /// Unique identifier for the adjustment
  int? id;

  /// Date of the inventory adjustment/count
  DateTime adjustmentDate;

  /// Product ID being adjusted
  int productId;

  /// Actual quantity counted in the store
  int countedQuantity;

  /// Recorded store quantity before adjustment
  int recordedStoreQuantity;

  /// Difference between counted and recorded quantity
  int difference;

  /// Retail price of the product at the time of adjustment
  double retailPriceAtAdjustment;

  /// Value of the adjustment (difference * retail price)
  double adjustmentValue;

  /// Optional notes about the adjustment
  String? notes;

  /// Product name for display purposes (not stored in database)
  String? productName;

  /// Constructor for creating a StoreInventoryAdjustment instance
  StoreInventoryAdjustment({
    this.id,
    required this.adjustmentDate,
    required this.productId,
    required this.countedQuantity,
    required this.recordedStoreQuantity,
    required this.difference,
    required this.retailPriceAtAdjustment,
    required this.adjustmentValue,
    this.notes,
    this.productName,
  });

  /// Converts the StoreInventoryAdjustment instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'adjustmentDate': adjustmentDate.millisecondsSinceEpoch,
      'productId': productId,
      'countedQuantity': countedQuantity,
      'recordedStoreQuantity': recordedStoreQuantity,
      'difference': difference,
      'retailPriceAtAdjustment': retailPriceAtAdjustment,
      'adjustmentValue': adjustmentValue,
      'notes': notes,
    };
  }

  /// Creates a StoreInventoryAdjustment instance from a Map (typically from database)
  factory StoreInventoryAdjustment.fromMap(Map<String, dynamic> map) {
    return StoreInventoryAdjustment(
      id: map['id'] as int?,
      adjustmentDate:
          DateTime.fromMillisecondsSinceEpoch(map['adjustmentDate'] as int),
      productId: map['productId'] as int,
      countedQuantity: map['countedQuantity'] as int,
      recordedStoreQuantity: map['recordedStoreQuantity'] as int,
      difference: map['difference'] as int,
      retailPriceAtAdjustment:
          map['retailPriceAtAdjustment']?.toDouble() ?? 0.0,
      adjustmentValue: map['adjustmentValue']?.toDouble() ?? 0.0,
      notes: map['notes'] as String?,
      productName:
          map['productName'] as String?, // This might come from JOIN queries
    );
  }

  /// Creates a copy of this StoreInventoryAdjustment with the given fields replaced
  StoreInventoryAdjustment copyWith({
    int? id,
    DateTime? adjustmentDate,
    int? productId,
    int? countedQuantity,
    int? recordedStoreQuantity,
    int? difference,
    double? retailPriceAtAdjustment,
    double? adjustmentValue,
    String? notes,
    String? productName,
  }) {
    return StoreInventoryAdjustment(
      id: id ?? this.id,
      adjustmentDate: adjustmentDate ?? this.adjustmentDate,
      productId: productId ?? this.productId,
      countedQuantity: countedQuantity ?? this.countedQuantity,
      recordedStoreQuantity:
          recordedStoreQuantity ?? this.recordedStoreQuantity,
      difference: difference ?? this.difference,
      retailPriceAtAdjustment:
          retailPriceAtAdjustment ?? this.retailPriceAtAdjustment,
      adjustmentValue: adjustmentValue ?? this.adjustmentValue,
      notes: notes ?? this.notes,
      productName: productName ?? this.productName,
    );
  }

  /// String representation of the StoreInventoryAdjustment
  @override
  String toString() {
    return 'StoreInventoryAdjustment{id: $id, adjustmentDate: $adjustmentDate, productId: $productId, '
        'countedQuantity: $countedQuantity, recordedStoreQuantity: $recordedStoreQuantity, '
        'difference: $difference, retailPriceAtAdjustment: $retailPriceAtAdjustment, '
        'adjustmentValue: $adjustmentValue, notes: $notes, productName: $productName}';
  }

  /// Equality comparison
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is StoreInventoryAdjustment &&
        other.id == id &&
        other.adjustmentDate == adjustmentDate &&
        other.productId == productId &&
        other.countedQuantity == countedQuantity &&
        other.recordedStoreQuantity == recordedStoreQuantity &&
        other.difference == difference &&
        other.retailPriceAtAdjustment == retailPriceAtAdjustment &&
        other.adjustmentValue == adjustmentValue &&
        other.notes == notes &&
        other.productName == productName;
  }

  /// Hash code
  @override
  int get hashCode {
    return id.hashCode ^
        adjustmentDate.hashCode ^
        productId.hashCode ^
        countedQuantity.hashCode ^
        recordedStoreQuantity.hashCode ^
        difference.hashCode ^
        retailPriceAtAdjustment.hashCode ^
        adjustmentValue.hashCode ^
        notes.hashCode ^
        productName.hashCode;
  }

  /// Calculate adjustment value based on difference and retail price
  static double calculateAdjustmentValue(int difference, double retailPrice) {
    return difference * retailPrice;
  }

  /// Get formatted adjustment date
  String get formattedAdjustmentDate {
    return '${adjustmentDate.day}/${adjustmentDate.month}/${adjustmentDate.year}';
  }

  /// Get formatted adjustment date with time
  String get formattedAdjustmentDateTime {
    return '${adjustmentDate.day}/${adjustmentDate.month}/${adjustmentDate.year} '
        '${adjustmentDate.hour.toString().padLeft(2, '0')}:'
        '${adjustmentDate.minute.toString().padLeft(2, '0')}';
  }

  /// Get formatted adjustment value
  String get formattedAdjustmentValue {
    return '${adjustmentValue.toStringAsFixed(2)} ر.س';
  }

  /// Check if this is a positive adjustment (gain)
  bool get isPositiveAdjustment => difference > 0;

  /// Check if this is a negative adjustment (loss)
  bool get isNegativeAdjustment => difference < 0;

  /// Check if this is a neutral adjustment (no change)
  bool get isNeutralAdjustment => difference == 0;

  /// Get adjustment type as string
  String get adjustmentType {
    if (isPositiveAdjustment) return 'زيادة';
    if (isNegativeAdjustment) return 'نقص';
    return 'لا يوجد تغيير';
  }

  /// Get adjustment description
  String get adjustmentDescription {
    if (isNeutralAdjustment) {
      return 'لا يوجد تغيير في الكمية';
    }

    final int absValue = difference.abs();
    if (isPositiveAdjustment) {
      return 'زيادة $absValue قطعة';
    } else {
      return 'نقص $absValue قطعة';
    }
  }
}
