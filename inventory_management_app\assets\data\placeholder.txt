# مجلد البيانات

هذا المجلد مخصص لحفظ ملفات البيانات الثابتة والنماذج.

## الملفات المطلوبة:
- sample_products.json (منتجات نموذجية)
- sample_customers.json (عملاء نموذجيون)
- categories.json (فئات المنتجات)
- units.json (وحدات القياس)
- currencies.json (العملات المدعومة)
- countries.json (قائمة الدول)

## الاستخدام:
```dart
String data = await rootBundle.loadString('assets/data/sample_products.json');
Map<String, dynamic> jsonData = json.decode(data);
```
