/// Model class representing an item in a sale transaction
class SaleItem {
  /// Unique identifier for the sale item
  int? id;

  /// Sale ID that this item belongs to
  int? saleId;

  /// Product ID for this sale item
  int? productId;

  /// Quantity of the product sold
  double? quantity;

  /// Price per unit for this sale item
  double? price;

  /// Product name for display purposes
  String? productName;

  /// حقل جديد لنظام المخزن والبقالة
  /// نوع البند: 'wholesale' للجملة أو 'retail' للتجزئة
  String? itemType;

  /// Unit price (alias for price)
  double? get unitPrice => price;

  /// Total price for this item (quantity * unit price)
  double? get totalPrice => (quantity ?? 0) * (price ?? 0);

  /// Constructor for creating a SaleItem instance
  SaleItem({
    this.id,
    this.saleId,
    this.productId,
    this.quantity,
    this.price,
    this.productName,
    this.itemType,
    double? unitPrice, // Accept unitPrice parameter
    double? totalPrice, // Accept totalPrice parameter (calculated)
  }) {
    if (unitPrice != null) {
      price = unitPrice;
    }
  }

  /// Converts the SaleItem instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'saleId': saleId,
      'productId': productId,
      'quantity': quantity,
      'price': price,
      'productName': productName,
      'itemType': itemType,
    };
  }

  /// Creates a SaleItem instance from a Map (typically from database)
  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      id: map['id'] as int?,
      saleId: map['saleId'] as int?,
      productId: map['productId'] as int?,
      quantity: map['quantity']?.toDouble(),
      price: map['price']?.toDouble(),
      productName: map['productName'] as String?,
      itemType: map['itemType'] as String?,
    );
  }

  @override
  String toString() {
    return 'SaleItem{id: $id, saleId: $saleId, productId: $productId, '
        'quantity: $quantity, price: $price, productName: $productName}';
  }

  /// Copy with method for creating modified copies
  SaleItem copyWith({
    int? id,
    int? saleId,
    int? productId,
    String? productName,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    String? itemType,
  }) {
    return SaleItem(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? price,
      itemType: itemType ?? this.itemType,
    );
  }
}
