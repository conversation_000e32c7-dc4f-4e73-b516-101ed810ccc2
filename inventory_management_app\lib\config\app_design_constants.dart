/// ثوابت التصميم للتطبيق - أسامة ماركت
library app_design_constants;

import 'package:flutter/material.dart';

/// فئة OnboardingData لبيانات شاشات التعريف
class OnboardingData {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const OnboardingData({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

/// ثوابت التصميم والألوان والأحجام
class AppDesignConstants {
  // ==================== الألوان ====================

  /// اللون الأساسي للتطبيق
  static const Color primaryColor = Color(0xFF1976D2);

  /// اللون الثانوي
  static const Color accentColor = Color(0xFFFF9800);

  /// لون النص الأساسي
  static const Color textPrimaryColor = Color(0xFF212121);

  /// لون النص الثانوي
  static const Color textSecondaryColor = Color(0xFF757575);

  /// لون الخلفية
  static const Color backgroundColor = Color(0xFFFAFAFA);

  /// لون الخطأ
  static const Color errorColor = Color(0xFFD32F2F);

  /// لون النجاح
  static const Color successColor = Color(0xFF388E3C);

  // ==================== التدرجات ====================

  /// تدرج شاشة البداية
  static const LinearGradient splashGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, accentColor],
  );

  /// تدرج شاشات التعريف - خلفية بيضاء
  static const LinearGradient onboardingGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Colors.white, Color(0xFFFAFAFA)],
  );

  // ==================== الأحجام والمسافات ====================

  /// مسافة صغيرة جداً
  static const double extraSmallPadding = 4.0;

  /// مسافة صغيرة
  static const double smallPadding = 8.0;

  /// المسافة الافتراضية
  static const double defaultPadding = 16.0;

  /// مسافة كبيرة
  static const double largePadding = 24.0;

  /// مسافة كبيرة جداً
  static const double extraLargePadding = 32.0;

  // ==================== أحجام الخط ====================

  /// حجم خط صغير
  static const double captionFontSize = 12.0;

  /// حجم خط النص العادي
  static const double bodyFontSize = 14.0;

  /// حجم خط العنوان الفرعي
  static const double subtitleFontSize = 16.0;

  /// حجم خط العنوان
  static const double titleFontSize = 20.0;

  /// حجم خط العنوان الكبير
  static const double headlineFontSize = 24.0;

  // ==================== أحجام الأيقونات ====================

  /// حجم أيقونة صغيرة
  static const double smallIconSize = 16.0;

  /// حجم أيقونة افتراضية
  static const double defaultIconSize = 24.0;

  /// حجم أيقونة كبيرة
  static const double largeIconSize = 32.0;

  /// حجم أيقونة كبيرة جداً
  static const double extraLargeIconSize = 48.0;

  /// حجم أيقونة شاشة البداية
  static const double splashIconSize = 80.0;

  // ==================== أحجام الحدود ====================

  /// نصف قطر حد صغير
  static const double smallBorderRadius = 4.0;

  /// نصف قطر الحد الافتراضي
  static const double defaultBorderRadius = 8.0;

  /// نصف قطر حد كبير
  static const double largeBorderRadius = 12.0;

  /// نصف قطر حد كبير جداً
  static const double extraLargeBorderRadius = 16.0;

  // ==================== أحجام الأزرار ====================

  /// ارتفاع الزر الافتراضي
  static const double buttonHeight = 48.0;

  /// ارتفاع الزر الصغير
  static const double smallButtonHeight = 36.0;

  // ==================== مدة الحركات ====================

  /// مدة حركة قصيرة
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);

  /// مدة حركة متوسطة
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);

  /// مدة حركة طويلة
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  /// مدة شاشة البداية
  static const Duration splashDuration = Duration(seconds: 3);

  /// مدة حركة شاشة البداية
  static const Duration splashAnimationDuration = Duration(milliseconds: 1500);

  // ==================== الظلال ====================

  /// ظل البطاقة
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8.0,
      offset: Offset(0, 2),
    ),
  ];

  // ==================== النصوص والمحتوى ====================

  /// اسم التطبيق
  static const String appName = 'أسامة ماركت';

  /// شعار التطبيق
  static const String appSlogan = 'نظام إدارة المخزون الذكي';

  /// مفتاح مشاهدة شاشات التعريف
  static const String hasViewedOnboardingKey = 'has_viewed_onboarding';

  // ==================== أنماط النصوص ====================

  /// نمط عنوان شاشة البداية - خط القاهرة الثقيل
  static const TextStyle splashTitleStyle = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 32,
    fontWeight: FontWeight.w700,
    color: textPrimaryColor,
    letterSpacing: 1.2,
    height: 1.2,
  );

  /// نمط العنوان الفرعي لشاشة البداية - خط القاهرة المتوسط
  static const TextStyle splashSubtitleStyle = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: textSecondaryColor,
    letterSpacing: 0.5,
    height: 1.4,
  );

  /// نمط عنوان شاشات التعريف - لون داكن للخلفية البيضاء
  static const TextStyle onboardingTitleStyle = TextStyle(
    fontFamily: 'Cairo',
    fontSize: titleFontSize,
    fontWeight: FontWeight.bold,
    color: Color(0xFF212121), // أسود داكن
  );

  /// نمط وصف شاشات التعريف - لون رمادي داكن للخلفية البيضاء
  static const TextStyle onboardingDescriptionStyle = TextStyle(
    fontFamily: 'Cairo',
    fontSize: bodyFontSize,
    color: Color(0xFF424242), // رمادي داكن
    height: 1.5,
  );

  // ==================== أنماط الأزرار ====================

  /// نمط الزر الأساسي
  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: Colors.white,
    minimumSize: const Size(double.infinity, buttonHeight),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(defaultBorderRadius),
    ),
  );

  // ==================== بيانات شاشات التعريف ====================

  /// صفحات شاشات التعريف
  static const List<OnboardingData> onboardingPages = [
    OnboardingData(
      title: 'إدارة المخزون بسهولة',
      description: 'تتبع منتجاتك وكمياتها بطريقة ذكية ومنظمة',
      icon: Icons.inventory_2,
      color: primaryColor,
    ),
    OnboardingData(
      title: 'مبيعات ومشتريات',
      description: 'سجل عمليات البيع والشراء وتابع أرباحك',
      icon: Icons.point_of_sale,
      color: accentColor,
    ),
    OnboardingData(
      title: 'تقارير مفصلة',
      description: 'احصل على تقارير شاملة عن أداء متجرك',
      icon: Icons.analytics,
      color: successColor,
    ),
    OnboardingData(
      title: 'ابدأ الآن',
      description: 'جاهز لإدارة متجرك بطريقة احترافية؟',
      icon: Icons.rocket_launch,
      color: primaryColor,
    ),
  ];

  /// النصوص التحفيزية
  static const List<String> motivationalTexts = [
    'النجاح يبدأ بخطوة واحدة',
    'نظم متجرك واحصل على النتائج',
    'البيانات الدقيقة تعني قرارات صحيحة',
    'مرحباً بك في عالم الإدارة الذكية!',
  ];
}
