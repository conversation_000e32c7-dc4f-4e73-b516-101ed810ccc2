import 'package:sqflite/sqflite.dart';
import '../models/sale.dart';
import '../models/sale_item.dart';
import 'database_service.dart';

/// Service class for handling Sale CRUD operations
class SaleService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all sales from the database
  Future<List<Sale>> getAllSales() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('sales');

    return List.generate(maps.length, (int i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// Get a sale by its ID
  Future<Sale?> getSaleById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sales',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Sale.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new sale into the database
  Future<int> insertSale(Sale sale) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'sales',
      sale.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing sale in the database
  Future<int> updateSale(Sale sale) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'sales',
      sale.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[sale.id],
    );
  }

  /// Delete a sale from the database
  Future<int> deleteSale(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'sales',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Get sales by customer
  Future<List<Sale>> getSalesByCustomer(int customerId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sales',
      where: 'customerId = ?',
      whereArgs: <Object?>[customerId],
    );

    return List.generate(maps.length, (int i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// Get sales by date range
  Future<List<Sale>> getSalesByDateRange(
      String startDate, String endDate) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sales',
      where: 'date BETWEEN ? AND ?',
      whereArgs: <Object?>[startDate, endDate],
    );

    return List.generate(maps.length, (int i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// Get total sales amount
  Future<double> getTotalSalesAmount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT SUM(total) as total FROM sales');
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get total number of sales
  Future<int> getSaleCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT COUNT(*) as count FROM sales');
    return result.first['count'] as int;
  }

  /// Get sale items for a specific sale
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sale_items',
      where: 'saleId = ?',
      whereArgs: <Object?>[saleId],
    );

    return List.generate(maps.length, (int i) {
      return SaleItem.fromMap(maps[i]);
    });
  }

  /// Insert sale item
  Future<int> insertSaleItem(SaleItem saleItem) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'sale_items',
      saleItem.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Delete sale items for a specific sale
  Future<int> deleteSaleItems(int saleId) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'sale_items',
      where: 'saleId = ?',
      whereArgs: <Object?>[saleId],
    );
  }

  /// Add a new sale (alias for insertSale)
  Future<int> addSale(Sale sale) async {
    return await insertSale(sale);
  }

  /// Get total sales for a specific period
  Future<double> getTotalSalesForPeriod(
      DateTime startDate, DateTime endDate) async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result = await db.rawQuery(
      'SELECT SUM(total) as total FROM sales WHERE date BETWEEN ? AND ?',
      <String>[
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
    );
    return (result.first['total'] as double?) ?? 0.0;
  }
}
