import 'package:flutter/material.dart';

class AnimatedListWidget<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(T item, int index, Animation<double> animation)
      itemBuilder;
  final Duration animationDuration;

  const AnimatedListWidget({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedListWidget<T>> createState() => _AnimatedListWidgetState<T>();
}

class _AnimatedListWidgetState<T> extends State<AnimatedListWidget<T>> {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<T> _items = <T>[];

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  @override
  void didUpdateWidget(AnimatedListWidget<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateList(oldWidget.items, widget.items);
  }

  void _updateList(List<T> oldItems, List<T> newItems) {
    // Add new items
    for (int i = 0; i < newItems.length; i++) {
      if (i >= oldItems.length || newItems[i] != oldItems[i]) {
        _items.insert(i, newItems[i]);
        _listKey.currentState
            ?.insertItem(i, duration: widget.animationDuration);
      }
    }

    // Remove deleted items
    for (int i = oldItems.length - 1; i >= 0; i--) {
      if (i >= newItems.length || !newItems.contains(oldItems[i])) {
        final removedItem = _items.removeAt(i);
        _listKey.currentState?.removeItem(
          i,
          (BuildContext context, Animation<double> animation) =>
              widget.itemBuilder(removedItem, i, animation),
          duration: widget.animationDuration,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedList(
      key: _listKey,
      initialItemCount: _items.length,
      itemBuilder:
          (BuildContext context, int index, Animation<double> animation) {
        if (index < _items.length) {
          return widget.itemBuilder(_items[index], index, animation);
        }
        return const SizedBox.shrink();
      },
    );
  }
}

class FadeInListTile extends StatelessWidget {
  final Widget child;
  final Animation<double> animation;

  const FadeInListTile({
    super.key,
    required this.child,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: animation.drive(
          Tween(begin: const Offset(1, 0), end: Offset.zero),
        ),
        child: child,
      ),
    );
  }
}

class ScaleInListTile extends StatelessWidget {
  final Widget child;
  final Animation<double> animation;

  const ScaleInListTile({
    super.key,
    required this.child,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: animation,
      child: child,
    );
  }
}
