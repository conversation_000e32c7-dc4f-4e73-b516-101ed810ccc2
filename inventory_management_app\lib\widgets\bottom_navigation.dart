import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../config/app_colors.dart';

/// شريط التنقل السفلي المحسن
class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        selectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          fontFamily: 'Tajawal',
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.normal,
          fontFamily: 'Tajawal',
        ),
        elevation: 0,
        onTap: (int index) => _onItemTapped(context, index),
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.dashboard_outlined, 0),
            activeIcon: _buildIcon(Icons.dashboard, 0),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.inventory_2_outlined, 1),
            activeIcon: _buildIcon(Icons.inventory_2, 1),
            label: 'المنتجات',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.people_outline, 2),
            activeIcon: _buildIcon(Icons.people, 2),
            label: 'العملاء',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.local_shipping_outlined, 3),
            activeIcon: _buildIcon(Icons.local_shipping, 3),
            label: 'الموردين',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.settings_outlined, 4),
            activeIcon: _buildIcon(Icons.settings, 4),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }

  /// بناء الأيقونة مع تأثير بصري
  Widget _buildIcon(IconData icon, int index) {
    final bool isSelected = currentIndex == index;

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: isSelected
          ? BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: Icon(
        icon,
        size: 24,
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
      ),
    );
  }

  /// معالجة النقر على عنصر التنقل
  void _onItemTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/suppliers');
        break;
      case 4:
        context.go('/settings');
        break;
    }
  }
}

/// مساعد لتحديد الفهرس الحالي بناءً على المسار
class BottomNavigationHelper {
  static int getCurrentIndex(String location) {
    if (location == '/' || location.startsWith('/dashboard')) {
      return 0;
    } else if (location.startsWith('/products')) {
      return 1;
    } else if (location.startsWith('/customers')) {
      return 2;
    } else if (location.startsWith('/suppliers')) {
      return 3;
    } else if (location.startsWith('/settings')) {
      return 4;
    }
    return 0; // افتراضي
  }
}

/// شريط تنقل عائم للشاشات الفرعية
class FloatingBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final List<FloatingNavItem> items;
  final Function(int) onTap;

  const FloatingBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.items,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: const <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children:
            items.asMap().entries.map((MapEntry<int, FloatingNavItem> entry) {
          final int index = entry.key;
          final FloatingNavItem item = entry.value;
          final bool isSelected = currentIndex == index;

          return GestureDetector(
            onTap: () => onTap(index),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Icon(
                    isSelected ? item.activeIcon : item.icon,
                    color: isSelected
                        ? AppColors.primary
                        : AppColors.textSecondary,
                    size: 20,
                  ),
                  if (isSelected) ...<Widget>[
                    const SizedBox(width: 8),
                    Text(
                      item.label,
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// عنصر التنقل العائم
class FloatingNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const FloatingNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}

/// شريط تنقل جانبي للشاشات الكبيرة
class SideNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const SideNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 240,
      color: AppColors.surface,
      child: Column(
        children: <Widget>[
          // شعار التطبيق
          Container(
            padding: const EdgeInsets.all(24),
            child: const Row(
              children: <Widget>[
                Icon(
                  Icons.store,
                  color: AppColors.primary,
                  size: 32,
                ),
                SizedBox(width: 12),
                Text(
                  'إدارة المخزون',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 0.5),

          // عناصر التنقل
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: <Widget>[
                _buildSideNavItem(
                  context,
                  icon: Icons.dashboard,
                  label: 'لوحة التحكم',
                  index: 0,
                ),
                _buildSideNavItem(
                  context,
                  icon: Icons.inventory_2,
                  label: 'المنتجات',
                  index: 1,
                ),
                _buildSideNavItem(
                  context,
                  icon: Icons.people,
                  label: 'العملاء والموردين',
                  index: 2,
                ),
                _buildSideNavItem(
                  context,
                  icon: Icons.point_of_sale,
                  label: 'المبيعات',
                  index: 3,
                ),
                _buildSideNavItem(
                  context,
                  icon: Icons.shopping_cart,
                  label: 'المشتريات',
                  index: 4,
                ),
                _buildSideNavItem(
                  context,
                  icon: Icons.analytics,
                  label: 'التقارير',
                  index: 5,
                ),
                const Divider(height: 1, thickness: 0.5),
                _buildSideNavItem(
                  context,
                  icon: Icons.settings,
                  label: 'الإعدادات',
                  index: 6,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSideNavItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required int index,
  }) {
    final bool isSelected = currentIndex == index;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? AppColors.primary : AppColors.textSecondary,
        ),
        title: Text(
          label,
          style: TextStyle(
            color: isSelected ? AppColors.primary : AppColors.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontFamily: 'Tajawal',
          ),
        ),
        selected: isSelected,
        selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: () => onTap(index),
      ),
    );
  }
}
