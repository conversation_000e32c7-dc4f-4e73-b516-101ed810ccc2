/// مساعد الموارد لتطبيق أسامة ماركت
/// 
/// يوفر طرق موحدة لتحميل واستخدام جميع الموارد في التطبيق
/// مثل الصور والأيقونات والبيانات النموذجية
/// 
/// الاستخدام:
/// ```dart
/// final String data = await AssetsHelper.loadSampleProducts();
/// final Widget image = AssetsHelper.getImage('logo.png');
/// ```

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// كلاس مساعد الموارد
/// 
/// يوفر طرق ثابتة للوصول لجميع الموارد في التطبيق
class AssetsHelper {
  
  // مسارات الموارد الأساسية
  static const String _imagesPath = 'assets/images/';
  static const String _iconsPath = 'assets/icons/';
  static const String _dataPath = 'assets/data/';
  static const String _samplesPath = 'assets/samples/';
  static const String _fontsPath = 'assets/fonts/';

  /// تحميل صورة من مجلد الصور
  /// 
  /// [imageName] - اسم الصورة مع الامتداد
  /// [width] - عرض الصورة (اختياري)
  /// [height] - ارتفاع الصورة (اختياري)
  /// [fit] - طريقة ملء الصورة (اختياري)
  /// Returns: Widget يحتوي على الصورة
  static Widget getImage(
    String imageName, {
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
  }) {
    return Image.asset(
      '$_imagesPath$imageName',
      width: width,
      height: height,
      fit: fit ?? BoxFit.contain,
      color: color,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width ?? 50,
          height: height ?? 50,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.image_not_supported,
            color: Colors.grey,
          ),
        );
      },
    );
  }

  /// تحميل أيقونة من مجلد الأيقونات
  /// 
  /// [iconName] - اسم الأيقونة مع الامتداد
  /// [size] - حجم الأيقونة (اختياري)
  /// [color] - لون الأيقونة (اختياري)
  /// Returns: Widget يحتوي على الأيقونة
  static Widget getIcon(
    String iconName, {
    double? size,
    Color? color,
  }) {
    return Image.asset(
      '$_iconsPath$iconName',
      width: size ?? 24,
      height: size ?? 24,
      color: color,
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          Icons.image_not_supported,
          size: size ?? 24,
          color: color ?? Colors.grey,
        );
      },
    );
  }

  /// تحميل أيقونة كـ ImageIcon
  /// 
  /// [iconName] - اسم الأيقونة مع الامتداد
  /// [size] - حجم الأيقونة (اختياري)
  /// [color] - لون الأيقونة (اختياري)
  /// Returns: ImageIcon
  static ImageIcon getImageIcon(
    String iconName, {
    double? size,
    Color? color,
  }) {
    return ImageIcon(
      AssetImage('$_iconsPath$iconName'),
      size: size ?? 24,
      color: color,
    );
  }

  /// تحميل ملف JSON من مجلد البيانات
  /// 
  /// [fileName] - اسم الملف مع الامتداد
  /// Returns: String يحتوي على محتوى الملف
  static Future<String> loadDataFile(String fileName) async {
    try {
      return await rootBundle.loadString('$_dataPath$fileName');
    } catch (e) {
      throw Exception('خطأ في تحميل ملف البيانات: $fileName - $e');
    }
  }

  /// تحميل ملف JSON وتحويله لـ Map
  /// 
  /// [fileName] - اسم الملف مع الامتداد
  /// Returns: Map يحتوي على البيانات المحولة
  static Future<Map<String, dynamic>> loadJsonData(String fileName) async {
    try {
      final String jsonString = await loadDataFile(fileName);
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw Exception('خطأ في تحويل ملف JSON: $fileName - $e');
    }
  }

  /// تحميل بيانات المنتجات النموذجية
  /// 
  /// Returns: Map يحتوي على بيانات المنتجات
  static Future<Map<String, dynamic>> loadSampleProducts() async {
    return await loadJsonData('sample_products.json');
  }

  /// تحميل بيانات العملاء النموذجية
  /// 
  /// Returns: Map يحتوي على بيانات العملاء
  static Future<Map<String, dynamic>> loadSampleCustomers() async {
    return await loadJsonData('sample_customers.json');
  }

  /// تحميل ملف من مجلد النماذج
  /// 
  /// [fileName] - اسم الملف مع الامتداد
  /// Returns: String يحتوي على محتوى الملف
  static Future<String> loadSampleFile(String fileName) async {
    try {
      return await rootBundle.loadString('$_samplesPath$fileName');
    } catch (e) {
      throw Exception('خطأ في تحميل ملف النموذج: $fileName - $e');
    }
  }

  /// فحص وجود ملف في الموارد
  /// 
  /// [assetPath] - المسار الكامل للملف
  /// Returns: true إذا كان الملف موجود
  static Future<bool> assetExists(String assetPath) async {
    try {
      await rootBundle.load(assetPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// فحص وجود صورة
  /// 
  /// [imageName] - اسم الصورة مع الامتداد
  /// Returns: true إذا كانت الصورة موجودة
  static Future<bool> imageExists(String imageName) async {
    return await assetExists('$_imagesPath$imageName');
  }

  /// فحص وجود أيقونة
  /// 
  /// [iconName] - اسم الأيقونة مع الامتداد
  /// Returns: true إذا كانت الأيقونة موجودة
  static Future<bool> iconExists(String iconName) async {
    return await assetExists('$_iconsPath$iconName');
  }

  /// الحصول على قائمة الفئات من البيانات النموذجية
  /// 
  /// Returns: List يحتوي على أسماء الفئات
  static Future<List<String>> getProductCategories() async {
    try {
      final Map<String, dynamic> data = await loadSampleProducts();
      final List<dynamic> categories = data['categories'] ?? [];
      return categories.cast<String>();
    } catch (e) {
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'حبوب',
        'زيوت',
        'سكريات',
        'مشروبات',
        'معلبات',
        'دقيق',
        'ألبان',
        'عسل',
        'معكرونة',
        'أخرى'
      ];
    }
  }

  /// الحصول على قائمة الوحدات من البيانات النموذجية
  /// 
  /// Returns: List يحتوي على أسماء الوحدات
  static Future<List<String>> getProductUnits() async {
    try {
      final Map<String, dynamic> data = await loadSampleProducts();
      final List<dynamic> units = data['units'] ?? [];
      return units.cast<String>();
    } catch (e) {
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'قطعة',
        'كيس',
        'علبة',
        'زجاجة',
        'برطمان',
        'كيلو',
        'جرام',
        'لتر',
        'أخرى'
      ];
    }
  }

  /// الحصول على قائمة الأحياء من بيانات العملاء
  /// 
  /// Returns: List يحتوي على أسماء الأحياء
  static Future<List<String>> getCustomerDistricts() async {
    try {
      final Map<String, dynamic> data = await loadSampleCustomers();
      final List<dynamic> districts = data['districts'] ?? [];
      return districts.cast<String>();
    } catch (e) {
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'حي النزهة',
        'حي الملز',
        'حي العليا',
        'حي الروضة',
        'حي السليمانية',
        'أخرى'
      ];
    }
  }

  /// الحصول على قائمة طرق الدفع
  /// 
  /// Returns: List يحتوي على طرق الدفع
  static Future<List<String>> getPaymentMethods() async {
    try {
      final Map<String, dynamic> data = await loadSampleCustomers();
      final List<dynamic> methods = data['payment_methods'] ?? [];
      return methods.cast<String>();
    } catch (e) {
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'نقدي',
        'آجل',
        'بطاقة ائتمان',
        'تحويل بنكي',
        'محفظة إلكترونية'
      ];
    }
  }

  /// تحميل خط مخصص
  /// 
  /// [fontName] - اسم الخط
  /// Returns: String يحتوي على اسم عائلة الخط
  static String getCustomFont(String fontName) {
    switch (fontName.toLowerCase()) {
      case 'cairo':
        return 'Cairo';
      case 'tajawal':
        return 'Tajawal';
      default:
        return 'Cairo'; // الخط الافتراضي
    }
  }

  /// الحصول على مسارات جميع الموارد
  /// 
  /// Returns: Map يحتوي على مسارات الموارد
  static Map<String, String> getAssetsPaths() {
    return {
      'images': _imagesPath,
      'icons': _iconsPath,
      'data': _dataPath,
      'samples': _samplesPath,
      'fonts': _fontsPath,
    };
  }

  /// طباعة معلومات الموارد للتطوير
  /// 
  /// يطبع قائمة بجميع مسارات الموارد المتاحة
  static void printAssetsInfo() {
    print('=== معلومات الموارد ===');
    final Map<String, String> paths = getAssetsPaths();
    paths.forEach((String key, String value) {
      print('$key: $value');
    });
    print('=====================');
  }
}
