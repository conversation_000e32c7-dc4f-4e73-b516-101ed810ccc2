/// Model class representing an internal transfer of inventory from warehouse to store
class InternalTransfer {
  /// Unique identifier for the transfer
  int? id;

  /// Date of the transfer operation
  DateTime transferDate;

  /// Product ID being transferred
  int productId;

  /// Quantity being transferred
  int transferredQuantity;

  /// Wholesale price of the product at the time of transfer
  double wholesalePriceAtTransfer;

  /// Retail price of the product at the time of transfer
  double retailPriceAtTransfer;

  /// Total wholesale value of the transferred quantity
  double totalWholesaleValue;

  /// Total retail value of the transferred quantity
  double totalRetailValue;

  /// Optional notes about the transfer
  String? notes;

  /// Product name for display purposes (not stored in database)
  String? productName;

  /// Constructor for creating an InternalTransfer instance
  InternalTransfer({
    this.id,
    required this.transferDate,
    required this.productId,
    required this.transferredQuantity,
    required this.wholesalePriceAtTransfer,
    required this.retailPriceAtTransfer,
    required this.totalWholesaleValue,
    required this.totalRetailValue,
    this.notes,
    this.productName,
  });

  /// Converts the InternalTransfer instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'transferDate': transferDate.millisecondsSinceEpoch,
      'productId': productId,
      'transferredQuantity': transferredQuantity,
      'wholesalePriceAtTransfer': wholesalePriceAtTransfer,
      'retailPriceAtTransfer': retailPriceAtTransfer,
      'totalWholesaleValue': totalWholesaleValue,
      'totalRetailValue': totalRetailValue,
      'notes': notes,
    };
  }

  /// Creates an InternalTransfer instance from a Map (typically from database)
  factory InternalTransfer.fromMap(Map<String, dynamic> map) {
    return InternalTransfer(
      id: map['id'] as int?,
      transferDate: DateTime.fromMillisecondsSinceEpoch(map['transferDate'] as int),
      productId: map['productId'] as int,
      transferredQuantity: map['transferredQuantity'] as int,
      wholesalePriceAtTransfer: map['wholesalePriceAtTransfer']?.toDouble() ?? 0.0,
      retailPriceAtTransfer: map['retailPriceAtTransfer']?.toDouble() ?? 0.0,
      totalWholesaleValue: map['totalWholesaleValue']?.toDouble() ?? 0.0,
      totalRetailValue: map['totalRetailValue']?.toDouble() ?? 0.0,
      notes: map['notes'] as String?,
      productName: map['productName'] as String?, // This might come from JOIN queries
    );
  }

  /// Creates a copy of this InternalTransfer with the given fields replaced
  InternalTransfer copyWith({
    int? id,
    DateTime? transferDate,
    int? productId,
    int? transferredQuantity,
    double? wholesalePriceAtTransfer,
    double? retailPriceAtTransfer,
    double? totalWholesaleValue,
    double? totalRetailValue,
    String? notes,
    String? productName,
  }) {
    return InternalTransfer(
      id: id ?? this.id,
      transferDate: transferDate ?? this.transferDate,
      productId: productId ?? this.productId,
      transferredQuantity: transferredQuantity ?? this.transferredQuantity,
      wholesalePriceAtTransfer: wholesalePriceAtTransfer ?? this.wholesalePriceAtTransfer,
      retailPriceAtTransfer: retailPriceAtTransfer ?? this.retailPriceAtTransfer,
      totalWholesaleValue: totalWholesaleValue ?? this.totalWholesaleValue,
      totalRetailValue: totalRetailValue ?? this.totalRetailValue,
      notes: notes ?? this.notes,
      productName: productName ?? this.productName,
    );
  }

  /// String representation of the InternalTransfer
  @override
  String toString() {
    return 'InternalTransfer{id: $id, transferDate: $transferDate, productId: $productId, '
           'transferredQuantity: $transferredQuantity, wholesalePriceAtTransfer: $wholesalePriceAtTransfer, '
           'retailPriceAtTransfer: $retailPriceAtTransfer, totalWholesaleValue: $totalWholesaleValue, '
           'totalRetailValue: $totalRetailValue, notes: $notes, productName: $productName}';
  }

  /// Equality comparison
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is InternalTransfer &&
      other.id == id &&
      other.transferDate == transferDate &&
      other.productId == productId &&
      other.transferredQuantity == transferredQuantity &&
      other.wholesalePriceAtTransfer == wholesalePriceAtTransfer &&
      other.retailPriceAtTransfer == retailPriceAtTransfer &&
      other.totalWholesaleValue == totalWholesaleValue &&
      other.totalRetailValue == totalRetailValue &&
      other.notes == notes &&
      other.productName == productName;
  }

  /// Hash code
  @override
  int get hashCode {
    return id.hashCode ^
      transferDate.hashCode ^
      productId.hashCode ^
      transferredQuantity.hashCode ^
      wholesalePriceAtTransfer.hashCode ^
      retailPriceAtTransfer.hashCode ^
      totalWholesaleValue.hashCode ^
      totalRetailValue.hashCode ^
      notes.hashCode ^
      productName.hashCode;
  }

  /// Calculate total wholesale value based on quantity and price
  static double calculateTotalWholesaleValue(int quantity, double wholesalePrice) {
    return quantity * wholesalePrice;
  }

  /// Calculate total retail value based on quantity and price
  static double calculateTotalRetailValue(int quantity, double retailPrice) {
    return quantity * retailPrice;
  }

  /// Get formatted transfer date
  String get formattedTransferDate {
    return '${transferDate.day}/${transferDate.month}/${transferDate.year}';
  }

  /// Get formatted transfer date with time
  String get formattedTransferDateTime {
    return '${transferDate.day}/${transferDate.month}/${transferDate.year} '
           '${transferDate.hour.toString().padLeft(2, '0')}:'
           '${transferDate.minute.toString().padLeft(2, '0')}';
  }
}
