import 'package:sqflite/sqflite.dart';
import '../models/purchase.dart';
import '../models/purchase_item.dart';
import 'database_service.dart';

/// Service class for handling Purchase CRUD operations
class PurchaseService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all purchases from the database
  Future<List<Purchase>> getAllPurchases() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('purchases');

    return List.generate(maps.length, (int i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// Get a purchase by its ID
  Future<Purchase?> getPurchaseById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchases',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Purchase.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new purchase into the database
  Future<int> insertPurchase(Purchase purchase) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'purchases',
      purchase.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing purchase in the database
  Future<int> updatePurchase(Purchase purchase) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'purchases',
      purchase.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[purchase.id],
    );
  }

  /// Delete a purchase from the database
  Future<int> deletePurchase(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'purchases',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Get purchases by supplier
  Future<List<Purchase>> getPurchasesBySupplier(int supplierId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchases',
      where: 'supplierId = ?',
      whereArgs: <Object?>[supplierId],
    );

    return List.generate(maps.length, (int i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// Get purchases by date range
  Future<List<Purchase>> getPurchasesByDateRange(
      String startDate, String endDate) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchases',
      where: 'date BETWEEN ? AND ?',
      whereArgs: <Object?>[startDate, endDate],
    );

    return List.generate(maps.length, (int i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// Get total purchases amount
  Future<double> getTotalPurchasesAmount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT SUM(total) as total FROM purchases');
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get total number of purchases
  Future<int> getPurchaseCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT COUNT(*) as count FROM purchases');
    return result.first['count'] as int;
  }

  /// Get purchase items for a specific purchase
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchase_items',
      where: 'purchaseId = ?',
      whereArgs: <Object?>[purchaseId],
    );

    return List.generate(maps.length, (int i) {
      return PurchaseItem.fromMap(maps[i]);
    });
  }

  /// Insert purchase item
  Future<int> insertPurchaseItem(PurchaseItem purchaseItem) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'purchase_items',
      purchaseItem.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Delete purchase items for a specific purchase
  Future<int> deletePurchaseItems(int purchaseId) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'purchase_items',
      where: 'purchaseId = ?',
      whereArgs: <Object?>[purchaseId],
    );
  }
}
