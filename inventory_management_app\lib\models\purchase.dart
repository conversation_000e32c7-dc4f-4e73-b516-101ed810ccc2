/// Model class for Purchase (فاتورة التوريد)
class Purchase {
  /// Unique identifier for the purchase
  int? id;

  /// ID of the supplier
  int? supplierId;

  /// Date of the purchase (ISO string format)
  String? date;

  /// Total amount of the purchase
  double? total;

  /// Additional notes for the purchase
  String? notes;

  /// Status of the purchase (completed, pending, cancelled)
  String? status;

  /// Invoice number for the purchase
  String? invoiceNumber;

  /// Supplier name for display purposes
  String? supplierName;

  /// Legacy compatibility getters
  DateTime? get purchaseDate => date != null ? DateTime.parse(date!) : null;
  double? get totalAmount => total;

  /// Constructor for creating a Purchase instance
  Purchase({
    this.id,
    this.supplierId,
    this.date,
    this.total,
    this.notes,
    this.status,
    this.invoiceNumber,
    this.supplierName,
    DateTime? purchaseDate,
    double? totalAmount,
  }) {
    // Handle legacy parameters
    if (purchaseDate != null) {
      date = purchaseDate.toIso8601String();
    }
    if (totalAmount != null) {
      total = totalAmount;
    }
  }

  /// Converts the Purchase instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'supplierId': supplierId,
      'date': date,
      'total': total,
      'notes': notes,
      'status': status,
      'invoiceNumber': invoiceNumber,
      'supplierName': supplierName,
    };
  }

  /// Creates a Purchase instance from a Map (typically from database)
  factory Purchase.fromMap(Map<String, dynamic> map) {
    return Purchase(
      id: map['id'] as int?,
      supplierId: map['supplierId'] as int?,
      date: map['date'] as String?,
      total: map['total']?.toDouble(),
      notes: map['notes'] as String?,
      status: map['status'] as String?,
      invoiceNumber: map['invoiceNumber'] as String?,
      supplierName: map['supplierName'] as String?,
    );
  }

  @override
  String toString() {
    return 'Purchase{id: $id, supplierId: $supplierId, date: $date, '
        'total: $total, notes: $notes, status: $status}';
  }
}
