import 'package:flutter/material.dart';
import '../config/app_colors.dart';
import '../config/app_styles.dart';
import '../config/app_dimensions.dart';
import 'custom_buttons.dart';

/// نافذة تأكيد محسنة مع تصميم عصري
class EnhancedConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final IconData? icon;
  final Color? iconColor;
  final bool isDanger;

  const EnhancedConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'تأكيد',
    this.cancelText = 'إلغاء',
    this.onConfirm,
    this.onCancel,
    this.icon,
    this.iconColor,
    this.isDanger = false,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      contentPadding: const EdgeInsets.all(AppDimensions.paddingL),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // الأيقونة
          if (icon != null) ...<Widget>[
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: (iconColor ??
                        (isDanger ? AppColors.error : AppColors.primary))
                    .withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
              ),
              child: Icon(
                icon,
                size: AppDimensions.iconXL,
                color: iconColor ??
                    (isDanger ? AppColors.error : AppColors.primary),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
          ],

          // العنوان
          Text(
            title,
            style: AppStyles.titleLarge.copyWith(
              color: isDanger ? AppColors.error : AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppDimensions.paddingS),

          // الرسالة
          Text(
            message,
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppDimensions.paddingL),

          // الأزرار
          Row(
            children: <Widget>[
              Expanded(
                child: CustomSecondaryButton(
                  text: cancelText,
                  onPressed: onCancel ?? () => Navigator.of(context).pop(false),
                  isFullWidth: true,
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: isDanger
                    ? CustomDangerButton(
                        text: confirmText,
                        onPressed:
                            onConfirm ?? () => Navigator.of(context).pop(true),
                        isFullWidth: true,
                      )
                    : CustomPrimaryButton(
                        text: confirmText,
                        onPressed:
                            onConfirm ?? () => Navigator.of(context).pop(true),
                        isFullWidth: true,
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// عرض نافذة تأكيد عامة
  static Future<bool?> show(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    IconData? icon,
    Color? iconColor,
    bool isDanger = false,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => EnhancedConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        icon: icon,
        iconColor: iconColor,
        isDanger: isDanger,
      ),
    );
  }

  /// عرض نافذة تأكيد الحذف
  static Future<bool?> showDelete(
    BuildContext context, {
    required String itemName,
    String? customMessage,
  }) {
    return show(
      context,
      title: 'تأكيد الحذف',
      message: customMessage ??
          'هل أنت متأكد من حذف "$itemName"؟\nلا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      icon: Icons.delete_forever,
      isDanger: true,
    );
  }

  /// عرض نافذة تأكيد الحفظ
  static Future<bool?> showSave(
    BuildContext context, {
    String? customMessage,
  }) {
    return show(
      context,
      title: 'تأكيد الحفظ',
      message: customMessage ?? 'هل تريد حفظ التغييرات؟',
      confirmText: 'حفظ',
      cancelText: 'إلغاء',
      icon: Icons.save,
    );
  }

  /// عرض نافذة تأكيد إتمام الفاتورة
  static Future<bool?> showCompleteInvoice(
    BuildContext context, {
    required String invoiceType,
    required double total,
  }) {
    return show(
      context,
      title: 'تأكيد إتمام $invoiceType',
      message:
          'هل أنت متأكد من إتمام $invoiceType بمبلغ ${total.toStringAsFixed(2)} ر.س؟\nسيتم تحديث المخزون تلقائياً.',
      confirmText: 'إتمام',
      cancelText: 'إلغاء',
      icon: Icons.receipt_long,
    );
  }
}
