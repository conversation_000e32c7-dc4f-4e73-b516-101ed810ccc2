import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/sale_provider.dart';
import '../../models/sale.dart';
import '../../utils/formatters.dart';
import 'sale_details_screen.dart';

class SalesScreen extends StatelessWidget {
  const SalesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final SaleProvider saleProvider = Provider.of<SaleProvider>(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المبيعات'),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (BuildContext context) =>
                        const SaleDetailsScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        body: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: saleProvider.sales.length,
          itemBuilder: (BuildContext context, int index) {
            final Sale sale = saleProvider.sales[index];
            return Card(
              child: ListTile(
                title: Text('بيع رقم: ${sale.id}'),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text('العميل: ${sale.customerId ?? 'غير محدد'}'),
                    Text(
                      'التاريخ: ${sale.date != null ? sale.date.toString().split(' ')[0] : 'غير محدد'}',
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text('${sale.total?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                    const Icon(Icons.arrow_forward),
                  ],
                ),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute<void>(
                      builder: (BuildContext context) => SaleDetailsScreen(
                        saleId: sale.id,
                        isReadOnly: true,
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
