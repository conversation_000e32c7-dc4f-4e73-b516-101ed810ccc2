import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/store_inventory_provider.dart';
import '../../providers/product_provider.dart';
import '../../models/product.dart';
import '../dialogs/select_product_for_adjustment_dialog.dart';
import '../dialogs/confirmation_dialog.dart';

class StoreInventoryAdjustmentScreen extends StatefulWidget {
  const StoreInventoryAdjustmentScreen({super.key});

  @override
  State<StoreInventoryAdjustmentScreen> createState() =>
      _StoreInventoryAdjustmentScreenState();
}

class _StoreInventoryAdjustmentScreenState
    extends State<StoreInventoryAdjustmentScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _adjustmentDateController =
      TextEditingController();
  final TextEditingController _productNameController = TextEditingController();
  final TextEditingController _countedQuantityController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  Product? _selectedProduct;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _adjustmentDateController.text = _formatDate(_selectedDate);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  int get _recordedStoreQuantity {
    return _selectedProduct?.storeQuantity ?? 0;
  }

  int get _countedQuantity {
    return int.tryParse(_countedQuantityController.text) ?? 0;
  }

  int get _difference {
    return _countedQuantity - _recordedStoreQuantity;
  }

  double get _adjustmentValue {
    if (_selectedProduct == null) return 0.0;
    return _difference * (_selectedProduct!.retailPrice ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('جرد مخزون البقالة'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveAdjustment,
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // تاريخ الجرد
                      TextFormField(
                        controller: _adjustmentDateController,
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الجرد',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: _selectDate,
                      ),

                      const SizedBox(height: 16),

                      // اختيار المنتج
                      TextFormField(
                        controller: _productNameController,
                        decoration: const InputDecoration(
                          labelText: 'المنتج',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.inventory_2),
                          suffixIcon: Icon(Icons.search),
                        ),
                        readOnly: true,
                        onTap: _selectProduct,
                        validator: (String? value) {
                          if (_selectedProduct == null) {
                            return 'يرجى اختيار المنتج';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // تفاصيل المنتج المحدد
                      if (_selectedProduct != null) ...<Widget>[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                'تفاصيل المنتج: ${_selectedProduct!.name}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                            'سعر التجزئة: ${_selectedProduct!.retailPrice?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                                        Text(
                                            'سعر الجملة: ${_selectedProduct!.price.toStringAsFixed(2) ?? '0.00'} ر.س'),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                          'الكمية المسجلة: $_recordedStoreQuantity',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.blue,
                                          ),
                                        ),
                                        Text(
                                            'المخزن: ${_selectedProduct!.warehouseQuantity?.toString() ?? '0'}'),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // الكمية الفعلية المعدودة
                      TextFormField(
                        controller: _countedQuantityController,
                        decoration: const InputDecoration(
                          labelText: 'الكمية الفعلية المعدودة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.numbers),
                          suffixText: 'قطعة',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الكمية المعدودة';
                          }
                          final int? quantity = int.tryParse(value);
                          if (quantity == null || quantity < 0) {
                            return 'يرجى إدخال كمية صحيحة';
                          }
                          return null;
                        },
                        onChanged: (String value) {
                          setState(() {}); // لتحديث القيم المحسوبة
                        },
                      ),

                      const SizedBox(height: 16),

                      // عرض الفرق والقيمة المحسوبة
                      if (_selectedProduct != null &&
                          _countedQuantityController
                              .text.isNotEmpty) ...<Widget>[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: _difference >= 0
                                ? Colors.green.shade50
                                : Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: _difference >= 0
                                  ? Colors.green.shade200
                                  : Colors.red.shade200,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                'نتيجة الجرد:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: _difference >= 0
                                      ? Colors.green
                                      : Colors.red,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text('الفرق: $_difference قطعة'),
                                        Text(
                                          _difference > 0
                                              ? 'زيادة في المخزون'
                                              : _difference < 0
                                                  ? 'نقص في المخزون'
                                                  : 'لا يوجد تغيير',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: _difference >= 0
                                                ? Colors.green
                                                : Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                            'قيمة التعديل: ${_adjustmentValue.toStringAsFixed(2)} ر.س'),
                                        Text(
                                          _adjustmentValue >= 0
                                              ? 'مكسب'
                                              : 'خسارة',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: _adjustmentValue >= 0
                                                ? Colors.green
                                                : Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // الملاحظات
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              // شريط الأزرار السفلي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveAdjustment,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'حفظ الجرد',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _adjustmentDateController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _selectProduct() async {
    final Product? selectedProduct = await showDialog<Product>(
      context: context,
      builder: (BuildContext context) =>
          const SelectProductForAdjustmentDialog(),
    );

    if (selectedProduct != null) {
      setState(() {
        _selectedProduct = selectedProduct;
        _productNameController.text = selectedProduct.name;
        _countedQuantityController.clear(); // مسح الكمية عند تغيير المنتج
      });
    }
  }

  Future<void> _saveAdjustment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedProduct == null) {
      _showErrorSnackBar('يرجى اختيار المنتج');
      return;
    }

    // عرض حوار التأكيد
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => ConfirmationDialog(
        title: 'تأكيد حفظ الجرد',
        content: 'هل أنت متأكد من حفظ الجرد؟\n\n'
            'سيتم تعديل كمية المنتج "${_selectedProduct!.name}" في البقالة من $_recordedStoreQuantity إلى $_countedQuantity.\n\n'
            'الفرق: $_difference قطعة\n'
            'قيمة التعديل: ${_adjustmentValue.toStringAsFixed(2)} ر.س',
        confirmText: 'حفظ الجرد',
        cancelText: 'إلغاء',
      ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final StoreInventoryProvider storeInventoryProvider =
          context.read<StoreInventoryProvider>();
      final ProductProvider productProvider = context.read<ProductProvider>();

      final bool success =
          await storeInventoryProvider.performStoreInventoryAdjustment(
        _selectedProduct!.id!,
        _countedQuantity,
        _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        productProvider,
      );

      if (success) {
        _showSuccessSnackBar('تم حفظ الجرد بنجاح');
        _resetForm();
      } else {
        _showErrorSnackBar(storeInventoryProvider.error ?? 'فشل في حفظ الجرد');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _resetForm() {
    setState(() {
      _selectedProduct = null;
      _productNameController.clear();
      _countedQuantityController.clear();
      _notesController.clear();
      _selectedDate = DateTime.now();
      _adjustmentDateController.text = _formatDate(_selectedDate);
    });
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _adjustmentDateController.dispose();
    _productNameController.dispose();
    _countedQuantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
