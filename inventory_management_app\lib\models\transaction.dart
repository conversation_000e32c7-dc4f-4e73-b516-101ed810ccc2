/// Model class representing a financial transaction
class Transaction {
  /// Unique identifier for the transaction
  int? id;
  
  /// Date of the transaction
  String? date;
  
  /// Description of the transaction
  String? description;
  
  /// Amount of the transaction
  double? amount;
  
  /// Type of transaction (e.g., "income", "expense")
  String? type;

  /// Constructor for creating a Transaction instance
  Transaction({
    this.id,
    this.date,
    this.description,
    this.amount,
    this.type,
  });

  /// Converts the Transaction instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'date': date,
      'description': description,
      'amount': amount,
      'type': type,
    };
  }

  /// Creates a Transaction instance from a Map (typically from database)
  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] as int?,
      date: map['date'] as String?,
      description: map['description'] as String?,
      amount: map['amount']?.toDouble(),
      type: map['type'] as String?,
    );
  }

  @override
  String toString() {
    return 'Transaction{id: $id, date: $date, description: $description, '
        'amount: $amount, type: $type}';
  }
}
