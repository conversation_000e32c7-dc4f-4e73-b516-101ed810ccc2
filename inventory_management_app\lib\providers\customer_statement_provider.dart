import 'package:flutter/material.dart';
import 'package:sqflite_common/sqlite_api.dart';
import '../models/customer.dart';
import '../models/sale.dart';
import '../services/database_service.dart';

class CustomerStatementTransaction {
  final DateTime date;
  final String description;
  final double amount;
  final String type; // 'debit' or 'credit'
  final double balance;
  final int? referenceId;

  CustomerStatementTransaction({
    required this.date,
    required this.description,
    required this.amount,
    required this.type,
    required this.balance,
    this.referenceId,
  });

  String get formattedAmount {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color get amountColor {
    return type == 'debit' ? Colors.red : Colors.green;
  }

  String get typeLabel {
    return type == 'debit' ? 'مدين' : 'دائن';
  }
}

class CustomerStatementProvider extends ChangeNotifier {
  final List<CustomerStatementTransaction> _transactions =
      <CustomerStatementTransaction>[];
  Customer? _selectedCustomer;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;
  String? _error;
  double _totalDebit = 0.0;
  double _totalCredit = 0.0;
  double _finalBalance = 0.0;

  // حقول جديدة لنظام الجملة والتجزئة
  double _totalWholesaleDebit = 0.0;
  double _totalRetailDebit = 0.0;
  double _remainingRetailDebt = 0.0;

  List<CustomerStatementTransaction> get transactions => _transactions;
  Customer? get selectedCustomer => _selectedCustomer;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get totalDebit => _totalDebit;
  double get totalCredit => _totalCredit;
  double get finalBalance => _finalBalance;

  // Getters للحقول الجديدة
  double get totalWholesaleDebit => _totalWholesaleDebit;
  double get totalRetailDebit => _totalRetailDebit;
  double get remainingRetailDebt => _remainingRetailDebt;

  String get formattedTotalDebit => '${_totalDebit.toStringAsFixed(2)} ر.س';
  String get formattedTotalCredit => '${_totalCredit.toStringAsFixed(2)} ر.س';
  String get formattedFinalBalance => '${_finalBalance.toStringAsFixed(2)} ر.س';

  // Formatted getters للحقول الجديدة
  String get formattedTotalWholesaleDebit =>
      '${_totalWholesaleDebit.toStringAsFixed(2)} ر.س';
  String get formattedTotalRetailDebit =>
      '${_totalRetailDebit.toStringAsFixed(2)} ر.س';
  String get formattedRemainingRetailDebt =>
      '${_remainingRetailDebt.toStringAsFixed(2)} ر.س';

  void setCustomer(Customer customer) {
    _selectedCustomer = customer;
    notifyListeners();
  }

  void setDateRange(DateTime? start, DateTime? end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }

  void clearSelection() {
    _selectedCustomer = null;
    _startDate = null;
    _endDate = null;
    _transactions.clear();
    _totalDebit = 0.0;
    _totalCredit = 0.0;
    _finalBalance = 0.0;
    _error = null;
    notifyListeners();
  }

  Future<void> loadCustomerStatement() async {
    if (_selectedCustomer == null) {
      _error = 'يرجى اختيار عميل أولاً';
      notifyListeners();
      return;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _transactions.clear();
      _totalDebit = 0.0;
      _totalCredit = 0.0;
      _finalBalance = 0.0;
      _totalWholesaleDebit = 0.0;
      _totalRetailDebit = 0.0;
      _remainingRetailDebt = 0.0;

      // Get customer sales within date range
      final List<Sale> sales = await _getCustomerSales();

      // Convert sales to transactions
      double runningBalance = 0.0;

      for (final Sale sale in sales) {
        final double totalAmount = sale.totalAmount ?? 0.0;
        final double wholesaleAmount = sale.totalWholesaleAmount ?? 0.0;
        final double retailAmount = sale.totalRetailAmount ?? 0.0;
        final double remainingRetailAmount = sale.remainingRetailAmount ?? 0.0;

        // إضافة فاتورة الجملة إذا كانت موجودة
        if (wholesaleAmount > 0) {
          runningBalance += wholesaleAmount;

          _transactions.add(CustomerStatementTransaction(
            date:
                sale.date is DateTime ? sale.date as DateTime : DateTime.now(),
            description: 'فاتورة بيع (جملة) #${sale.id}',
            amount: wholesaleAmount,
            type: 'debit',
            balance: runningBalance,
            referenceId: sale.id,
          ));

          _totalWholesaleDebit += wholesaleAmount;
        }

        // إضافة فاتورة التجزئة إذا كانت موجودة
        if (retailAmount > 0) {
          runningBalance += retailAmount;

          String description = 'فاتورة بيع (تجزئة) #${sale.id}';
          if (sale.notesForRetailItems != null &&
              sale.notesForRetailItems!.isNotEmpty) {
            description += ' - ${sale.notesForRetailItems}';
          }

          _transactions.add(CustomerStatementTransaction(
            date:
                sale.date is DateTime ? sale.date as DateTime : DateTime.now(),
            description: description,
            amount: retailAmount,
            type: 'debit',
            balance: runningBalance,
            referenceId: sale.id,
          ));

          _totalRetailDebit += retailAmount;
        }

        // إضافة الدين المتبقي من التجزئة إذا كان موجوداً
        if (remainingRetailAmount > 0) {
          _remainingRetailDebt += remainingRetailAmount;

          _transactions.add(CustomerStatementTransaction(
            date:
                sale.date is DateTime ? sale.date as DateTime : DateTime.now(),
            description: 'دين تجزئة متبقي #${sale.id}',
            amount: remainingRetailAmount,
            type: 'debit',
            balance: runningBalance + remainingRetailAmount,
            referenceId: sale.id,
          ));
        }

        _totalDebit += totalAmount;
      }

      // TODO: Add payment transactions when payment system is implemented
      // For now, we'll just show sales transactions

      _finalBalance = runningBalance;

      // Sort transactions by date
      _transactions.sort(
          (CustomerStatementTransaction a, CustomerStatementTransaction b) =>
              a.date.compareTo(b.date));
    } catch (e) {
      _error = 'حدث خطأ في تحميل كشف الحساب: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<Sale>> _getCustomerSales() async {
    try {
      final Database db = await DatabaseService.instance.database;

      String query = '''
        SELECT * FROM sales 
        WHERE customerId = ?
      ''';

      List<dynamic> params = <dynamic>[_selectedCustomer!.id];

      if (_startDate != null && _endDate != null) {
        query += ' AND date BETWEEN ? AND ?';
        params.addAll(<dynamic>[
          _startDate!.toIso8601String(),
          _endDate!.toIso8601String(),
        ]);
      }

      query += ' ORDER BY date ASC';

      final List<Map<String, Object?>> result =
          await db.rawQuery(query, params);

      return result
          .map((Map<String, Object?> map) => Sale.fromMap(map))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب مبيعات العميل: $e');
    }
  }

  void filterByPeriod(String period) {
    final DateTime now = DateTime.now();
    DateTime? start;
    DateTime? end = now;

    switch (period) {
      case 'اليوم':
        start = DateTime(now.year, now.month, now.day);
        break;
      case 'الأسبوع':
        start = now.subtract(Duration(days: now.weekday - 1));
        start = DateTime(start.year, start.month, start.day);
        break;
      case 'الشهر':
        start = DateTime(now.year, now.month, 1);
        break;
      case 'السنة':
        start = DateTime(now.year, 1, 1);
        break;
      default:
        start = null;
        end = null;
    }

    setDateRange(start, end);
  }

  // Export functionality
  String generateStatementText() {
    if (_selectedCustomer == null) return '';

    final StringBuffer buffer = StringBuffer();
    buffer.writeln('كشف حساب العميل');
    buffer.writeln('================');
    buffer.writeln('اسم العميل: ${_selectedCustomer!.name}');
    buffer.writeln('رقم الهاتف: ${_selectedCustomer!.phone ?? 'غير محدد'}');

    if (_startDate != null && _endDate != null) {
      buffer.writeln(
          'الفترة: من ${_startDate!.day}/${_startDate!.month}/${_startDate!.year} إلى ${_endDate!.day}/${_endDate!.month}/${_endDate!.year}');
    }

    buffer.writeln('');
    buffer.writeln('الحركات:');
    buffer.writeln('--------');

    for (final CustomerStatementTransaction transaction in _transactions) {
      buffer.writeln(
          '${transaction.formattedDate} - ${transaction.description} - ${transaction.formattedAmount} (${transaction.typeLabel})');
    }

    buffer.writeln('');
    buffer.writeln('الملخص:');
    buffer.writeln('-------');
    buffer.writeln('إجمالي المدين: $formattedTotalDebit');
    buffer.writeln('إجمالي الدائن: $formattedTotalCredit');
    buffer.writeln('الرصيد النهائي: $formattedFinalBalance');

    return buffer.toString();
  }
}
