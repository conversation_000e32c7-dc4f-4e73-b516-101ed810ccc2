import 'package:flutter/material.dart';

/// فئة الأيقونات المخصصة للتطبيق
class AppIcons {
  // أيقونات الشاشة الرئيسية
  static const IconData dashboard = Icons.dashboard;
  static const IconData home = Icons.home;

  // أيقونات المنتجات
  static const IconData products = Icons.inventory_2;
  static const IconData product = Icons.inventory;
  static const IconData addProduct = Icons.add_box;
  static const IconData editProduct = Icons.edit;
  static const IconData deleteProduct = Icons.delete;
  static const IconData productDetails = Icons.info;
  static const IconData barcode = Icons.qr_code;
  static const IconData scanner = Icons.qr_code_scanner;
  static const IconData category = Icons.category;
  static const IconData unit = Icons.straighten;
  static const IconData quantity = Icons.numbers;
  static const IconData stock = Icons.warehouse;
  static const IconData lowStock = Icons.warning;
  static const IconData outOfStock = Icons.error;

  // أيقونات العملاء
  static const IconData customers = Icons.people;
  static const IconData customer = Icons.person;
  static const IconData addCustomer = Icons.person_add;
  static const IconData customerDetails = Icons.account_circle;
  static const IconData phone = Icons.phone;
  static const IconData email = Icons.email;
  static const IconData address = Icons.location_on;
  static const IconData balance = Icons.account_balance_wallet;
  static const IconData debt = Icons.money_off;
  static const IconData credit = Icons.account_balance;

  // أيقونات الموردين
  static const IconData suppliers = Icons.local_shipping;
  static const IconData supplier = Icons.business;
  static const IconData addSupplier = Icons.add_business;
  static const IconData supplierDetails = Icons.store;

  // أيقونات المبيعات
  static const IconData sales = Icons.point_of_sale;
  static const IconData sale = Icons.receipt_long;
  static const IconData addSale = Icons.add_shopping_cart;
  static const IconData saleDetails = Icons.receipt;
  static const IconData invoice = Icons.description;
  static const IconData invoiceNumber = Icons.confirmation_number;
  static const IconData total = Icons.monetization_on;
  static const IconData payment = Icons.payment;
  static const IconData cash = Icons.money;
  static const IconData card = Icons.credit_card;
  static const IconData bankTransfer = Icons.account_balance;
  static const IconData check = Icons.check_box;
  static const IconData deferred = Icons.schedule;

  // أيقونات المشتريات
  static const IconData purchases = Icons.shopping_cart;
  static const IconData purchase = Icons.shopping_bag;
  static const IconData addPurchase = Icons.add_shopping_cart;
  static const IconData purchaseDetails = Icons.shopping_basket;

  // أيقونات المصروفات
  static const IconData expenses = Icons.money_off;
  static const IconData expense = Icons.receipt;
  static const IconData addExpense = Icons.add_circle;
  static const IconData expenseCategory = Icons.category;

  // أيقونات التقارير
  static const IconData reports = Icons.analytics;
  static const IconData chart = Icons.bar_chart;
  static const IconData pieChart = Icons.pie_chart;
  static const IconData lineChart = Icons.show_chart;
  static const IconData statistics = Icons.assessment;
  static const IconData export = Icons.file_download;
  static const IconData print = Icons.print;
  static const IconData share = Icons.share;

  // أيقونات الإعدادات
  static const IconData settings = Icons.settings;
  static const IconData profile = Icons.account_circle;
  static const IconData theme = Icons.palette;
  static const IconData language = Icons.language;
  static const IconData currency = Icons.attach_money;
  static const IconData notifications = Icons.notifications;
  static const IconData sound = Icons.volume_up;
  static const IconData vibration = Icons.vibration;
  static const IconData backup = Icons.backup;
  static const IconData restore = Icons.restore;
  static const IconData security = Icons.security;
  static const IconData privacy = Icons.privacy_tip;
  static const IconData help = Icons.help;
  static const IconData about = Icons.info;

  // أيقونات الحالة
  static const IconData success = Icons.check_circle;
  static const IconData error = Icons.error;
  static const IconData warning = Icons.warning;
  static const IconData info = Icons.info;
  static const IconData pending = Icons.pending;
  static const IconData completed = Icons.check_circle;
  static const IconData cancelled = Icons.cancel;
  static const IconData draft = Icons.drafts;

  // أيقونات الإجراءات
  static const IconData add = Icons.add;
  static const IconData edit = Icons.edit;
  static const IconData delete = Icons.delete;
  static const IconData save = Icons.save;
  static const IconData cancel = Icons.cancel;
  static const IconData confirm = Icons.check;
  static const IconData search = Icons.search;
  static const IconData filter = Icons.filter_list;
  static const IconData sort = Icons.sort;
  static const IconData refresh = Icons.refresh;
  static const IconData sync = Icons.sync;
  static const IconData upload = Icons.upload;
  static const IconData download = Icons.download;
  static const IconData copy = Icons.copy;
  static const IconData paste = Icons.paste;
  static const IconData cut = Icons.cut;

  // أيقونات التنقل
  static const IconData back = Icons.arrow_back;
  static const IconData forward = Icons.arrow_forward;
  static const IconData up = Icons.arrow_upward;
  static const IconData down = Icons.arrow_downward;
  static const IconData left = Icons.arrow_back_ios;
  static const IconData right = Icons.arrow_forward_ios;
  static const IconData menu = Icons.menu;
  static const IconData close = Icons.close;
  static const IconData expand = Icons.expand_more;
  static const IconData collapse = Icons.expand_less;

  // أيقونات التاريخ والوقت
  static const IconData date = Icons.calendar_today;
  static const IconData time = Icons.access_time;
  static const IconData dateRange = Icons.date_range;
  static const IconData schedule = Icons.schedule;
  static const IconData history = Icons.history;
  static const IconData today = Icons.today;
  static const IconData week = Icons.view_week;
  static const IconData month = Icons.view_module;
  static const IconData year = Icons.view_carousel;

  // أيقونات الملفات
  static const IconData file = Icons.insert_drive_file;
  static const IconData folder = Icons.folder;
  static const IconData pdf = Icons.picture_as_pdf;
  static const IconData excel = Icons.table_chart;
  static const IconData image = Icons.image;
  static const IconData document = Icons.description;
  static const IconData attachment = Icons.attach_file;

  // أيقونات الشبكة
  static const IconData wifi = Icons.wifi;
  static const IconData offline = Icons.wifi_off;
  static const IconData sync_problem = Icons.sync_problem;
  static const IconData cloud = Icons.cloud;
  static const IconData cloudUpload = Icons.cloud_upload;
  static const IconData cloudDownload = Icons.cloud_download;

  // أيقونات الأمان
  static const IconData lock = Icons.lock;
  static const IconData unlock = Icons.lock_open;
  static const IconData key = Icons.vpn_key;
  static const IconData fingerprint = Icons.fingerprint;
  static const IconData visibility = Icons.visibility;
  static const IconData visibilityOff = Icons.visibility_off;

  // أيقونات التواصل
  static const IconData call = Icons.call;
  static const IconData message = Icons.message;
  static const IconData chat = Icons.chat;
  static const IconData whatsapp = Icons.chat_bubble;
  static const IconData telegram = Icons.send;

  // أيقونات الطباعة
  static const IconData printer = Icons.print;
  static const IconData printSettings = Icons.print_disabled;
  static const IconData preview = Icons.preview;

  // أيقونات المتجر
  static const IconData store = Icons.store;
  static const IconData shop = Icons.shopping_bag;
  static const IconData cart = Icons.shopping_cart;
  static const IconData basket = Icons.shopping_basket;
  static const IconData checkout = Icons.shopping_cart_checkout;

  // أيقونات الاتجاهات
  static const IconData trending_up = Icons.trending_up;
  static const IconData trending_down = Icons.trending_down;
  static const IconData trending_flat = Icons.trending_flat;

  // أيقونات الجودة
  static const IconData star = Icons.star;
  static const IconData starBorder = Icons.star_border;
  static const IconData favorite = Icons.favorite;
  static const IconData favoriteBorder = Icons.favorite_border;
  static const IconData thumb_up = Icons.thumb_up;
  static const IconData thumb_down = Icons.thumb_down;

  // أيقونات الموقع
  static const IconData location = Icons.location_on;
  static const IconData locationOff = Icons.location_off;
  static const IconData map = Icons.map;
  static const IconData directions = Icons.directions;
  static const IconData gps = Icons.gps_fixed;

  // أيقونات الطقس
  static const IconData sunny = Icons.wb_sunny;
  static const IconData cloudy = Icons.wb_cloudy;
  static const IconData rainy = Icons.grain;

  // أيقونات الألوان
  static const IconData colorPalette = Icons.palette;
  static const IconData brush = Icons.brush;
  static const IconData format_paint = Icons.format_paint;

  // أيقونات الحجم
  static const IconData fullscreen = Icons.fullscreen;
  static const IconData fullscreenExit = Icons.fullscreen_exit;
  static const IconData zoom_in = Icons.zoom_in;
  static const IconData zoom_out = Icons.zoom_out;
  static const IconData fit_screen = Icons.fit_screen;

  // أيقونات الصوت
  static const IconData volume_up = Icons.volume_up;
  static const IconData volume_down = Icons.volume_down;
  static const IconData volume_off = Icons.volume_off;
  static const IconData volume_mute = Icons.volume_mute;

  // أيقونات الطاقة
  static const IconData battery_full = Icons.battery_full;
  static const IconData battery_charging = Icons.battery_charging_full;
  static const IconData battery_low = Icons.battery_alert;
  static const IconData power = Icons.power;
  static const IconData power_off = Icons.power_off;

  // أيقونات الاتصال
  static const IconData bluetooth = Icons.bluetooth;
  static const IconData bluetooth_disabled = Icons.bluetooth_disabled;
  static const IconData nfc = Icons.nfc;
  static const IconData usb = Icons.usb;

  // أيقونات التطبيق
  static const IconData app_icon = Icons.apps;
  static const IconData widget = Icons.widgets;
  static const IconData extension = Icons.extension;
  static const IconData plugin = Icons.power_input;

  /// الحصول على أيقونة حسب النوع
  static IconData getIconByType(String type) {
    switch (type.toLowerCase()) {
      case 'product':
      case 'products':
        return products;
      case 'customer':
      case 'customers':
        return customers;
      case 'supplier':
      case 'suppliers':
        return suppliers;
      case 'sale':
      case 'sales':
        return sales;
      case 'purchase':
      case 'purchases':
        return purchases;
      case 'expense':
      case 'expenses':
        return expenses;
      case 'report':
      case 'reports':
        return reports;
      case 'setting':
      case 'settings':
        return settings;
      default:
        return info;
    }
  }

  /// الحصول على أيقونة الحالة
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return success;
      case 'pending':
        return pending;
      case 'cancelled':
      case 'error':
        return error;
      case 'warning':
        return warning;
      case 'draft':
        return draft;
      default:
        return info;
    }
  }

  /// الحصول على أيقونة طريقة الدفع
  static IconData getPaymentIcon(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
      case 'نقداً':
        return cash;
      case 'card':
      case 'credit_card':
      case 'بطاقة':
        return card;
      case 'bank_transfer':
      case 'تحويل':
        return bankTransfer;
      case 'check':
      case 'شيك':
        return check;
      case 'deferred':
      case 'آجل':
        return deferred;
      default:
        return payment;
    }
  }
}
