1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.inventory_management_app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:15:5-67
15-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:15:22-64
16    <!-- أذونات الوصول لجهات الاتصال -->
17    <uses-permission android:name="android.permission.READ_CONTACTS" />
17-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:3:5-72
17-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:3:22-69
18    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
18-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:4:5-73
18-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:4:22-70
19    <uses-permission android:name="android.permission.GET_ACCOUNTS" /> <!-- أذونات الوصول للملفات (للاستيراد من إكسل) -->
19-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:5:5-71
19-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:5:22-68
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:8:5-80
20-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:8:22-77
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:9:5-81
21-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:9:22-78
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:10:5-76
22-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:10:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:11:5-75
23-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:11:22-72
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:12:5-75
24-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:12:22-72
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:16:5-79
25-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:16:22-76
26    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- أذونات الكاميرا (لمسح الباركود) -->
26-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:17:5-76
26-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:17:22-73
27    <uses-permission android:name="android.permission.CAMERA" />
27-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:20:5-65
27-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:20:22-62
28
29    <uses-feature
29-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:21:5-85
30        android:name="android.hardware.camera"
30-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:21:19-57
31        android:required="false" />
31-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:21:58-82
32    <uses-feature
32-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:22:5-95
33        android:name="android.hardware.camera.autofocus"
33-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:22:19-67
34        android:required="false" /> <!-- أذونات الموقع (لتحديد موقع المتجر) -->
34-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:22:68-92
35    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
35-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:25:5-79
35-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:25:22-76
36    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- أذونات الإشعارات -->
36-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:26:5-81
36-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:26:22-78
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:29:5-77
37-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:29:22-74
38    <uses-permission android:name="android.permission.VIBRATE" /> <!-- أذونات إضافية للتطبيق -->
38-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:30:5-66
38-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:30:22-63
39    <uses-permission android:name="android.permission.WAKE_LOCK" />
39-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:33:5-68
39-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:33:22-65
40    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
40-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:34:5-81
40-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:34:22-78
41    <!--
42 Required to query activities that can process text, see:
43         https://developer.android.com/training/package-visibility and
44         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
45
46         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
47    -->
48    <queries>
48-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:77:5-82:15
49        <intent>
49-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:78:9-81:18
50            <action android:name="android.intent.action.PROCESS_TEXT" />
50-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:79:13-72
50-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:79:21-70
51
52            <data android:mimeType="text/plain" />
52-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:80:13-50
52-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:80:19-48
53        </intent>
54        <intent>
54-->[:file_picker] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
55            <action android:name="android.intent.action.GET_CONTENT" />
55-->[:file_picker] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
55-->[:file_picker] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
56
57            <data android:mimeType="*/*" />
57-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:80:13-50
57-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:80:19-48
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
61-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
61-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
62
63    <permission
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.inventory_management_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.inventory_management_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
68
69    <application
70        android:name="android.app.Application"
71        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
72        android:debuggable="true"
73        android:extractNativeLibs="true"
74        android:icon="@mipmap/ic_launcher"
75        android:label="inventory_management_app" >
76        <activity
77            android:name="com.example.inventory_management_app.MainActivity"
78            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
79            android:exported="true"
80            android:hardwareAccelerated="true"
81            android:launchMode="singleTop"
82            android:taskAffinity=""
83            android:theme="@style/LaunchTheme"
84            android:windowSoftInputMode="adjustResize" >
85
86            <!-- تعطيل Impeller لحل مشكلة Vulkan -->
87            <meta-data
88                android:name="io.flutter.embedding.android.EnableImpeller"
89                android:value="false" />
90            <!--
91                 Specifies an Android theme to apply to this Activity as soon as
92                 the Android process has started. This theme is visible to the user
93                 while the Flutter UI initializes. After that, this theme continues
94                 to determine the Window background behind the Flutter UI.
95            -->
96            <meta-data
97                android:name="io.flutter.embedding.android.NormalTheme"
98                android:resource="@style/NormalTheme" />
99
100            <intent-filter>
101                <action android:name="android.intent.action.MAIN" />
102
103                <category android:name="android.intent.category.LAUNCHER" />
104            </intent-filter>
105        </activity>
106        <!--
107             Don't delete the meta-data below.
108             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
109        -->
110        <meta-data
111            android:name="flutterEmbedding"
112            android:value="2" />
113        <!--
114           Declares a provider which allows us to store files to share in
115           '.../caches/share_plus' and grant the receiving action access
116        -->
117        <provider
117-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
118            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
118-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
119            android:authorities="com.example.inventory_management_app.flutter.share_provider"
119-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
120            android:exported="false"
120-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
121            android:grantUriPermissions="true" >
121-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
122            <meta-data
122-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
123                android:name="android.support.FILE_PROVIDER_PATHS"
123-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
124                android:resource="@xml/flutter_share_file_paths" />
124-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
125        </provider>
126        <!--
127           This manifest declared broadcast receiver allows us to use an explicit
128           Intent when creating a PendingItent to be informed of the user's choice
129        -->
130        <receiver
130-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
131            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
131-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
132            android:exported="false" >
132-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
133            <intent-filter>
133-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
134                <action android:name="EXTRA_CHOSEN_COMPONENT" />
134-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
134-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
135            </intent-filter>
136        </receiver>
137
138        <provider
138-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
139            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
139-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
140            android:authorities="com.example.inventory_management_app.flutter.image_provider"
140-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
141            android:exported="false"
141-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
142            android:grantUriPermissions="true" >
142-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
143            <meta-data
143-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
145                android:resource="@xml/flutter_image_picker_file_paths" />
145-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
146        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
147        <service
147-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
148            android:name="com.google.android.gms.metadata.ModuleDependencies"
148-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
149            android:enabled="false"
149-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
150            android:exported="false" >
150-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
151            <intent-filter>
151-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
152                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
152-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
152-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
153            </intent-filter>
154
155            <meta-data
155-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
156                android:name="photopicker_activity:0:required"
156-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
157                android:value="" />
157-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
158        </service>
159
160        <activity
160-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
161            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
161-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
162            android:exported="false"
162-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
163            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
163-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
164
165        <provider
165-->[:printing] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
166            android:name="net.nfet.flutter.printing.PrintFileProvider"
166-->[:printing] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
167            android:authorities="com.example.inventory_management_app.flutter.printing"
167-->[:printing] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
168            android:exported="false"
168-->[:printing] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
169            android:grantUriPermissions="true" >
169-->[:printing] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
170            <meta-data
170-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
171                android:name="android.support.FILE_PROVIDER_PATHS"
171-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
172                android:resource="@xml/flutter_printing_file_paths" />
172-->[:share_plus] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
173        </provider>
174
175        <activity
175-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
176            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
176-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
177            android:excludeFromRecents="true"
177-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
178            android:exported="false"
178-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
179            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
179-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
180        <!--
181            Service handling Google Sign-In user revocation. For apps that do not integrate with
182            Google Sign-In, this service will never be started.
183        -->
184        <service
184-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
185            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
185-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
186            android:exported="true"
186-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
187            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
187-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
188            android:visibleToInstantApps="true" />
188-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
189
190        <activity
190-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
191            android:name="com.google.android.gms.common.api.GoogleApiActivity"
191-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
192            android:exported="false"
192-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
193            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
193-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
194
195        <meta-data
195-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
196            android:name="com.google.android.gms.version"
196-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
197            android:value="@integer/google_play_services_version" />
197-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
198
199        <provider
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
200            android:name="androidx.startup.InitializationProvider"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
201            android:authorities="com.example.inventory_management_app.androidx-startup"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
203            <meta-data
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
204                android:name="androidx.work.WorkManagerInitializer"
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
205                android:value="androidx.startup" />
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
206            <meta-data
206-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
207-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
208                android:value="androidx.startup" />
208-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
209            <meta-data
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
211                android:value="androidx.startup" />
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
212        </provider>
213
214        <service
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
215            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
217            android:enabled="@bool/enable_system_alarm_service_default"
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
218            android:exported="false" />
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
219        <service
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
220            android:name="androidx.work.impl.background.systemjob.SystemJobService"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
222            android:enabled="@bool/enable_system_job_service_default"
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
223            android:exported="true"
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
224            android:permission="android.permission.BIND_JOB_SERVICE" />
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
225        <service
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
226            android:name="androidx.work.impl.foreground.SystemForegroundService"
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
228            android:enabled="@bool/enable_system_foreground_service_default"
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
229            android:exported="false" />
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
230
231        <receiver
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
232            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
233            android:directBootAware="false"
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
234            android:enabled="true"
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
235            android:exported="false" />
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
236        <receiver
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
237            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
239            android:enabled="false"
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
240            android:exported="false" >
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
241            <intent-filter>
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
242                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
243                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
244            </intent-filter>
245        </receiver>
246        <receiver
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
247            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
249            android:enabled="false"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
250            android:exported="false" >
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
251            <intent-filter>
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
252                <action android:name="android.intent.action.BATTERY_OKAY" />
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
253                <action android:name="android.intent.action.BATTERY_LOW" />
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
254            </intent-filter>
255        </receiver>
256        <receiver
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
257            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
258            android:directBootAware="false"
258-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
259            android:enabled="false"
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
260            android:exported="false" >
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
261            <intent-filter>
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
262                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
263                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
264            </intent-filter>
265        </receiver>
266        <receiver
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
267            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
268            android:directBootAware="false"
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
269            android:enabled="false"
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
270            android:exported="false" >
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
271            <intent-filter>
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
272                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
273            </intent-filter>
274        </receiver>
275        <receiver
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
276            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
278            android:enabled="false"
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
279            android:exported="false" >
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
280            <intent-filter>
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
281                <action android:name="android.intent.action.BOOT_COMPLETED" />
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
282                <action android:name="android.intent.action.TIME_SET" />
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
283                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
284            </intent-filter>
285        </receiver>
286        <receiver
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
287            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
287-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
289            android:enabled="@bool/enable_system_alarm_service_default"
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
290            android:exported="false" >
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
291            <intent-filter>
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
292                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
293            </intent-filter>
294        </receiver>
295        <receiver
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
296            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
297            android:directBootAware="false"
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
298            android:enabled="true"
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
299            android:exported="true"
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
300            android:permission="android.permission.DUMP" >
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
301            <intent-filter>
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
302                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5be3a9150e91d2f050e5d7551acf339c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
303            </intent-filter>
304        </receiver>
305
306        <uses-library
306-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
307            android:name="androidx.window.extensions"
307-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
308            android:required="false" />
308-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
309        <uses-library
309-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
310            android:name="androidx.window.sidecar"
310-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
311            android:required="false" />
311-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
312
313        <receiver
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
314            android:name="androidx.profileinstaller.ProfileInstallReceiver"
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
315            android:directBootAware="false"
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
316            android:enabled="true"
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
317            android:exported="true"
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
318            android:permission="android.permission.DUMP" >
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
320                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
321            </intent-filter>
322            <intent-filter>
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
323                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
324            </intent-filter>
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
326                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
326-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
326-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
327            </intent-filter>
328            <intent-filter>
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
329                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
329-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
329-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
330            </intent-filter>
331        </receiver>
332
333        <service
333-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
334            android:name="androidx.room.MultiInstanceInvalidationService"
334-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
335            android:directBootAware="true"
335-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
336            android:exported="false" />
336-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
337    </application>
338
339</manifest>
