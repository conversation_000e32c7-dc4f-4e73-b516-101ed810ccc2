import 'dart:async';
import 'package:flutter/foundation.dart';
import '../utils/settings_helper.dart';

/// فئة مساعدة لمراقبة الأداء
class PerformanceHelper {
  static final Map<String, Stopwatch> _stopwatches = <String, Stopwatch>{};
  static final List<PerformanceMetric> _metrics = <PerformanceMetric>[];
  static bool _isEnabled = true;

  /// تهيئة مراقب الأداء
  static void initialize() {
    _isEnabled = SettingsHelper.getPerformanceMonitoring();
  }

  /// بدء قياس الأداء
  static void startMeasurement(String operationName) {
    if (!_isEnabled) return;

    final Stopwatch stopwatch = Stopwatch()..start();
    _stopwatches[operationName] = stopwatch;
  }

  /// إنهاء قياس الأداء
  static Duration? endMeasurement(String operationName) {
    if (!_isEnabled) return null;

    final Stopwatch? stopwatch = _stopwatches.remove(operationName);
    if (stopwatch == null) return null;

    stopwatch.stop();
    final Duration duration = stopwatch.elapsed;

    _addMetric(PerformanceMetric(
      operationName: operationName,
      duration: duration,
      timestamp: DateTime.now(),
    ));

    if (kDebugMode) {
      print('Performance: $operationName took ${duration.inMilliseconds}ms');
    }

    return duration;
  }

  /// قياس أداء دالة
  static Future<T> measureAsync<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    if (!_isEnabled) return await operation();

    startMeasurement(operationName);
    try {
      final result = await operation();
      return result;
    } finally {
      endMeasurement(operationName);
    }
  }

  /// قياس أداء دالة متزامنة
  static T measureSync<T>(
    String operationName,
    T Function() operation,
  ) {
    if (!_isEnabled) return operation();

    startMeasurement(operationName);
    try {
      final result = operation();
      return result;
    } finally {
      endMeasurement(operationName);
    }
  }

  /// إضافة مقياس أداء
  static void _addMetric(PerformanceMetric metric) {
    _metrics.add(metric);

    // الاحتفاظ بآخر 1000 مقياس فقط
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, _metrics.length - 1000);
    }
  }

  /// الحصول على مقاييس الأداء
  static List<PerformanceMetric> getMetrics({
    String? operationName,
    DateTime? since,
  }) {
    List<PerformanceMetric> filtered =
        _metrics.where((PerformanceMetric metric) {
      if (operationName != null && metric.operationName != operationName) {
        return false;
      }
      if (since != null && metric.timestamp.isBefore(since)) {
        return false;
      }
      return true;
    }).toList();

    return filtered;
  }

  /// الحصول على إحصائيات الأداء
  static PerformanceStats getStats({
    String? operationName,
    DateTime? since,
  }) {
    final List<PerformanceMetric> metrics =
        getMetrics(operationName: operationName, since: since);

    if (metrics.isEmpty) {
      return PerformanceStats(
        operationName: operationName ?? 'All',
        count: 0,
        totalDuration: Duration.zero,
        averageDuration: Duration.zero,
        minDuration: Duration.zero,
        maxDuration: Duration.zero,
      );
    }

    final List<Duration> durations =
        metrics.map((PerformanceMetric m) => m.duration).toList();
    durations.sort((Duration a, Duration b) => a.compareTo(b));

    final Duration totalDuration = durations.fold<Duration>(
      Duration.zero,
      (Duration sum, Duration duration) => sum + duration,
    );

    return PerformanceStats(
      operationName: operationName ?? 'All',
      count: metrics.length,
      totalDuration: totalDuration,
      averageDuration: Duration(
        microseconds: totalDuration.inMicroseconds ~/ metrics.length,
      ),
      minDuration: durations.first,
      maxDuration: durations.last,
    );
  }

  /// الحصول على العمليات البطيئة
  static List<PerformanceMetric> getSlowOperations({
    Duration threshold = const Duration(seconds: 1),
  }) {
    return _metrics
        .where((PerformanceMetric metric) => metric.duration >= threshold)
        .toList();
  }

  /// مسح مقاييس الأداء
  static void clearMetrics() {
    _metrics.clear();
  }

  /// تفعيل/إلغاء مراقبة الأداء
  static Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
    await SettingsHelper.setPerformanceMonitoring(enabled);

    if (!enabled) {
      clearMetrics();
      _stopwatches.clear();
    }
  }

  /// التحقق من تفعيل مراقبة الأداء
  static bool get isEnabled => _isEnabled;

  /// تصدير مقاييس الأداء
  static Map<String, dynamic> exportMetrics() {
    return <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'metrics': _metrics.map((PerformanceMetric m) => m.toMap()).toList(),
      'summary': _generateSummary(),
    };
  }

  /// إنشاء ملخص الأداء
  static Map<String, dynamic> _generateSummary() {
    final Set<String> operationNames =
        _metrics.map((PerformanceMetric m) => m.operationName).toSet();
    final Map<String, dynamic> summary = <String, dynamic>{};

    for (final String operationName in operationNames) {
      final PerformanceStats stats = getStats(operationName: operationName);
      summary[operationName] = stats.toMap();
    }

    return summary;
  }

  /// مراقبة استخدام الذاكرة
  static MemoryInfo getMemoryInfo() {
    // في Flutter، لا يمكن الحصول على معلومات الذاكرة مباشرة
    // لكن يمكن تقدير الاستخدام
    return MemoryInfo(
      usedMemory: 0, // TODO: تنفيذ قياس الذاكرة
      totalMemory: 0,
      availableMemory: 0,
    );
  }

  /// مراقبة معدل الإطارات
  static void startFrameRateMonitoring() {
    if (!_isEnabled) return;

    // TODO: تنفيذ مراقبة معدل الإطارات
  }

  /// إيقاف مراقبة معدل الإطارات
  static void stopFrameRateMonitoring() {
    // TODO: إيقاف مراقبة معدل الإطارات
  }

  /// الحصول على معدل الإطارات
  static double getCurrentFrameRate() {
    // TODO: الحصول على معدل الإطارات الحالي
    return 60.0; // قيمة افتراضية
  }

  /// تحليل الأداء
  static PerformanceAnalysis analyzePerformance() {
    final PerformanceStats allStats = getStats();
    final List<PerformanceMetric> slowOperations = getSlowOperations();
    final MemoryInfo memoryInfo = getMemoryInfo();
    final double frameRate = getCurrentFrameRate();

    return PerformanceAnalysis(
      overallStats: allStats,
      slowOperations: slowOperations,
      memoryInfo: memoryInfo,
      frameRate: frameRate,
      recommendations: _generateRecommendations(allStats, slowOperations),
    );
  }

  /// إنشاء توصيات الأداء
  static List<String> _generateRecommendations(
    PerformanceStats stats,
    List<PerformanceMetric> slowOperations,
  ) {
    final List<String> recommendations = <String>[];

    if (stats.averageDuration.inMilliseconds > 500) {
      recommendations.add('متوسط وقت العمليات مرتفع، يُنصح بتحسين الكود');
    }

    if (slowOperations.isNotEmpty) {
      recommendations
          .add('توجد ${slowOperations.length} عملية بطيئة تحتاج تحسين');
    }

    if (getCurrentFrameRate() < 30) {
      recommendations.add('معدل الإطارات منخفض، تحقق من العمليات المكلفة');
    }

    return recommendations;
  }
}

/// مقياس الأداء
class PerformanceMetric {
  final String operationName;
  final Duration duration;
  final DateTime timestamp;

  PerformanceMetric({
    required this.operationName,
    required this.duration,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'operationName': operationName,
      'duration': duration.inMicroseconds,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PerformanceMetric.fromMap(Map<String, dynamic> map) {
    return PerformanceMetric(
      operationName: map['operationName'],
      duration: Duration(microseconds: map['duration']),
      timestamp: DateTime.parse(map['timestamp']),
    );
  }
}

/// إحصائيات الأداء
class PerformanceStats {
  final String operationName;
  final int count;
  final Duration totalDuration;
  final Duration averageDuration;
  final Duration minDuration;
  final Duration maxDuration;

  PerformanceStats({
    required this.operationName,
    required this.count,
    required this.totalDuration,
    required this.averageDuration,
    required this.minDuration,
    required this.maxDuration,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'operationName': operationName,
      'count': count,
      'totalDuration': totalDuration.inMicroseconds,
      'averageDuration': averageDuration.inMicroseconds,
      'minDuration': minDuration.inMicroseconds,
      'maxDuration': maxDuration.inMicroseconds,
    };
  }
}

/// معلومات الذاكرة
class MemoryInfo {
  final int usedMemory;
  final int totalMemory;
  final int availableMemory;

  MemoryInfo({
    required this.usedMemory,
    required this.totalMemory,
    required this.availableMemory,
  });

  double get usagePercentage {
    if (totalMemory == 0) return 0.0;
    return (usedMemory / totalMemory) * 100;
  }
}

/// تحليل الأداء
class PerformanceAnalysis {
  final PerformanceStats overallStats;
  final List<PerformanceMetric> slowOperations;
  final MemoryInfo memoryInfo;
  final double frameRate;
  final List<String> recommendations;

  PerformanceAnalysis({
    required this.overallStats,
    required this.slowOperations,
    required this.memoryInfo,
    required this.frameRate,
    required this.recommendations,
  });
}
