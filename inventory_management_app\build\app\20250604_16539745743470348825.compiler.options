"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\app\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a6d62fc2e35431d66a6a237a46b81a05\\transformed\\jetified-libs.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\shared_preferences_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\workmanager\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\connectivity_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\device_info_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\file_picker\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\flutter_plugin_android_lifecycle\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\google_sign_in_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\image_picker_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\package_info_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\path_provider_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\permission_handler_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\printing\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\share_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\sqflite_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\url_launcher_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c22940fd007c288ac2172fb605aed0f1\\transformed\\jetified-flutter_embedding_debug-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0c43bb5fcbf5d527acc938eed5c5ee7\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\97724ced2d76c807da51dbdfe4264718\\transformed\\jetified-activity-1.9.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\905dd0fdc3aa478364757b63caeacb17\\transformed\\loader-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\115b656e1a4aecf880260d6d83623996\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c315d47e2f7879c3deeb8a2382af17bc\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\890fa23f232b0e27acd86381ae22e986\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\496b2ffad9f48ea28eeff97372eecba8\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0718b469eda36dc9592c0463fc44f0f5\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fef311fe87a7ffd53b02d1929ec2ecd6\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\15ba965be5d36f1409c507c4f4f01060\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8072e09447510b0de763fa662fba307a\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd0b64dbcc747184e3367538d3940e0\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fff59de3233a53a251731adb4c6ced0a\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ca08bea8da6ce44b915b59db148eb4a9\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2d84b4d753bef46c4f0c33072ed597d9\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\546c29fc3b12ba0ada2deef99fc20985\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\82f50a1147ea7962ad05f59f739641fb\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cfe501695087fe013590264201716ccd\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e789c606285d17c2ec99c03f43d33c98\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7ead9a6c3bb927c55881788d3824402d\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a963a3f078233bcb9f6b8b5c76988b4\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9f528df4bae2341bfd26279a570b8663\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ade16677ec891dea4619d807ecb70bab\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\997c71003b88b692b113760365ef0cc0\\transformed\\jetified-arm64_v8a_debug-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e89955f56e7726ef55ec1f78ddbe6412\\transformed\\multidex-2.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1a24e07e8946987328cdbf73d5c0d1c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cd60595a499747cfa90d326929eabb59\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e2cbe243dd93764f3ea77e6c23ccd98f\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e9b80c786d1f3bf9007353926f4b6eb1\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\app\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java" "C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\android\\app\\src\\main\\kotlin\\com\\example\\inventory_management_app\\MainActivity.kt"