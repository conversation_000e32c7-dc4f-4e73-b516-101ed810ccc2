/// Model class representing a measurement unit
class Unit {
  /// Unique identifier for the unit
  int? id;

  /// Name of the unit (e.g., "Kilogram", "Piece")
  String name;

  /// Symbol of the unit (e.g., "kg", "pcs")
  String? symbol;

  /// Constructor for creating a Unit instance
  Unit({
    this.id,
    required this.name,
    this.symbol,
  });

  /// Converts the Unit instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'symbol': symbol,
    };
  }

  /// Creates a Unit instance from a Map (typically from database)
  factory Unit.fromMap(Map<String, dynamic> map) {
    return Unit(
      id: map['id'] as int?,
      name: map['name'] as String? ?? '',
      symbol: map['symbol'] as String?,
    );
  }

  @override
  String toString() {
    return 'Unit{id: $id, name: $name, symbol: $symbol}';
  }
}
