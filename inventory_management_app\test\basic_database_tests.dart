/// اختبارات أساسية لقاعدة البيانات - أسامة ماركت
///
/// تركز على الوظائف الأساسية لقاعدة البيانات والجداول الأساسية
library basic_database_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  // تهيئة بيانات اللغة وقاعدة البيانات للاختبار
  setUpAll(() async {
    await initializeDateFormatting('ar', null);
    await initializeDateFormatting('ar_SA', null);
    
    // تهيئة databaseFactory للاختبارات
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
    
    // تفعيل وضع الاختبار مع قاعدة بيانات في الذاكرة
    DatabaseService.enableTestMode();
    await DatabaseService.instance.database;
  });

  // تنظيف قاعدة البيانات بعد كل اختبار
  tearDown(() async {
    await DatabaseService.instance.clearDatabaseForTesting();
  });

  // إغلاق قاعدة البيانات بعد جميع الاختبارات
  tearDownAll(() async {
    DatabaseService.disableTestMode();
    await DatabaseService.instance.resetDatabase();
  });

  group('اختبارات قاعدة البيانات الأساسية', () {
    
    test('يجب أن تتصل بقاعدة البيانات بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      expect(database, isNotNull);
      expect(database.isOpen, isTrue);
    });

    test('يجب أن تنشئ الجداول الأساسية', () async {
      final Database database = await DatabaseService.instance.database;
      
      // التحقق من وجود جدول المنتجات
      final List<Map<String, dynamic>> productTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='products'");
      expect(productTables.isNotEmpty, isTrue);
      
      // التحقق من وجود جدول العملاء
      final List<Map<String, dynamic>> customerTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='customers'");
      expect(customerTables.isNotEmpty, isTrue);
      
      // التحقق من وجود جدول المبيعات
      final List<Map<String, dynamic>> salesTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='sales'");
      expect(salesTables.isNotEmpty, isTrue);
    });

    test('يجب أن تضيف منتج بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة منتج باستخدام الحقول الأساسية فقط
      final Map<String, dynamic> productData = {
        'name': 'منتج اختبار',
        'price': 10.0,
        'description': 'وصف المنتج',
        'quantity': 100,
      };
      
      final int productId = await database.insert('products', productData);
      expect(productId, greaterThan(0));
      
      // التحقق من إضافة المنتج
      final List<Map<String, dynamic>> products = 
          await database.query('products', where: 'id = ?', whereArgs: [productId]);
      expect(products.length, equals(1));
      expect(products.first['name'], equals('منتج اختبار'));
    });

    test('يجب أن تضيف عميل بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة عميل باستخدام الحقول الأساسية فقط
      final Map<String, dynamic> customerData = {
        'name': 'عميل اختبار',
        'phone': '0501234567',
        'email': '<EMAIL>',
        'address': 'عنوان اختبار',
      };
      
      final int customerId = await database.insert('customers', customerData);
      expect(customerId, greaterThan(0));
      
      // التحقق من إضافة العميل
      final List<Map<String, dynamic>> customers = 
          await database.query('customers', where: 'id = ?', whereArgs: [customerId]);
      expect(customers.length, equals(1));
      expect(customers.first['name'], equals('عميل اختبار'));
    });

    test('يجب أن تضيف بيع بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة بيع باستخدام الحقول الأساسية فقط
      final Map<String, dynamic> saleData = {
        'customerId': 1,
        'date': DateTime.now().toIso8601String(),
        'total': 150.0,
        'notes': 'بيع اختبار',
      };
      
      final int saleId = await database.insert('sales', saleData);
      expect(saleId, greaterThan(0));
      
      // التحقق من إضافة البيع
      final List<Map<String, dynamic>> sales = 
          await database.query('sales', where: 'id = ?', whereArgs: [saleId]);
      expect(sales.length, equals(1));
      expect(sales.first['total'], equals(150.0));
    });

    test('يجب أن تحدث بيانات بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة منتج
      final Map<String, dynamic> originalData = {
        'name': 'منتج للتحديث',
        'price': 20.0,
        'description': 'وصف أصلي',
        'quantity': 50,
      };
      
      final int productId = await database.insert('products', originalData);
      
      // تحديث المنتج
      final Map<String, dynamic> updatedData = {
        'name': 'منتج محدث',
        'price': 25.0,
        'description': 'وصف محدث',
        'quantity': 75,
      };
      
      final int updatedRows = await database.update(
        'products', 
        updatedData,
        where: 'id = ?',
        whereArgs: [productId],
      );
      expect(updatedRows, equals(1));
      
      // التحقق من التحديث
      final List<Map<String, dynamic>> products = 
          await database.query('products', where: 'id = ?', whereArgs: [productId]);
      expect(products.first['name'], equals('منتج محدث'));
      expect(products.first['price'], equals(25.0));
    });

    test('يجب أن تحذف بيانات بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة عميل
      final Map<String, dynamic> customerData = {
        'name': 'عميل للحذف',
        'phone': '0509876543',
      };
      
      final int customerId = await database.insert('customers', customerData);
      
      // التحقق من وجود العميل
      List<Map<String, dynamic>> customers = 
          await database.query('customers', where: 'id = ?', whereArgs: [customerId]);
      expect(customers.length, equals(1));
      
      // حذف العميل
      final int deletedRows = await database.delete(
        'customers',
        where: 'id = ?',
        whereArgs: [customerId],
      );
      expect(deletedRows, equals(1));
      
      // التحقق من الحذف
      customers = await database.query('customers', where: 'id = ?', whereArgs: [customerId]);
      expect(customers.length, equals(0));
    });

    test('يجب أن تحسب إجماليات بشكل صحيح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة عدة مبيعات
      final List<Map<String, dynamic>> salesData = [
        {
          'customerId': 1,
          'date': DateTime.now().toIso8601String(),
          'total': 100.0,
          'notes': 'بيع 1',
        },
        {
          'customerId': 2,
          'date': DateTime.now().toIso8601String(),
          'total': 200.0,
          'notes': 'بيع 2',
        },
        {
          'customerId': 3,
          'date': DateTime.now().toIso8601String(),
          'total': 150.0,
          'notes': 'بيع 3',
        },
      ];
      
      // إضافة المبيعات إلى قاعدة البيانات
      for (final Map<String, dynamic> sale in salesData) {
        await database.insert('sales', sale);
      }
      
      // حساب إجمالي المبيعات
      final List<Map<String, dynamic>> totalResult = await database.rawQuery(
        'SELECT SUM(total) as totalSales FROM sales'
      );
      final double totalSales = totalResult.first['totalSales'] as double? ?? 0.0;
      expect(totalSales, equals(450.0));
      
      // عد المبيعات
      final List<Map<String, dynamic>> countResult = await database.rawQuery(
        'SELECT COUNT(*) as salesCount FROM sales'
      );
      final int salesCount = countResult.first['salesCount'] as int? ?? 0;
      expect(salesCount, equals(3));
    });

    test('يجب أن تنظف البيانات بشكل صحيح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة بيانات وهمية
      await database.insert('products', {
        'name': 'منتج للتنظيف',
        'price': 10.0,
        'description': 'وصف',
        'quantity': 100,
      });
      
      await database.insert('customers', {
        'name': 'عميل للتنظيف',
        'phone': '0501234567',
      });
      
      // التحقق من وجود البيانات
      final List<Map<String, dynamic>> productsBefore = await database.query('products');
      final List<Map<String, dynamic>> customersBefore = await database.query('customers');
      expect(productsBefore.length, greaterThan(0));
      expect(customersBefore.length, greaterThan(0));
      
      // تنظيف البيانات
      await DatabaseService.instance.clearDatabaseForTesting();
      
      // التحقق من حذف البيانات
      final List<Map<String, dynamic>> productsAfter = await database.query('products');
      final List<Map<String, dynamic>> customersAfter = await database.query('customers');
      expect(productsAfter.length, equals(0));
      expect(customersAfter.length, equals(0));
    });

    test('يجب أن تتعامل مع الاستعلامات المعقدة', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة منتجات متعددة
      final List<Map<String, dynamic>> productsData = [
        {'name': 'منتج أ', 'price': 10.0, 'description': 'وصف أ', 'quantity': 100},
        {'name': 'منتج ب', 'price': 20.0, 'description': 'وصف ب', 'quantity': 50},
        {'name': 'منتج ج', 'price': 15.0, 'description': 'وصف ج', 'quantity': 75},
      ];
      
      for (final Map<String, dynamic> product in productsData) {
        await database.insert('products', product);
      }
      
      // استعلام للمنتجات بسعر أكبر من 15
      final List<Map<String, dynamic>> expensiveProducts = await database.query(
        'products',
        where: 'price > ?',
        whereArgs: [15.0],
        orderBy: 'price DESC',
      );
      
      expect(expensiveProducts.length, equals(1));
      expect(expensiveProducts.first['name'], equals('منتج ب'));
      
      // استعلام لحساب متوسط السعر
      final List<Map<String, dynamic>> avgResult = await database.rawQuery(
        'SELECT AVG(price) as avgPrice FROM products'
      );
      final double avgPrice = avgResult.first['avgPrice'] as double? ?? 0.0;
      expect(avgPrice, equals(15.0));
    });
  });
}
