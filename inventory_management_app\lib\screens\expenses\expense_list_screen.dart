import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/expense_provider.dart';
import '../../models/expense.dart';
import 'expense_form_screen.dart';
import 'expense_details_screen.dart';

class ExpenseListScreen extends StatefulWidget {
  const ExpenseListScreen({super.key});

  @override
  State<ExpenseListScreen> createState() => _ExpenseListScreenState();
}

class _ExpenseListScreenState extends State<ExpenseListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategoryFilter = 'الكل';
  String _selectedPeriodFilter = 'الكل';
  DateTime? _startDate;
  DateTime? _endDate;

  final List<String> _categoryFilters = <String>[
    'الكل',
    'إيجار',
    'رواتب',
    'مستلزمات',
    'خدمات',
    'صيانة',
    'تسويق',
    'نقل ومواصلات',
    'متنوعة',
  ];

  final List<String> _periodFilters = <String>[
    'الكل',
    'اليوم',
    'الأسبوع',
    'الشهر',
    'السنة',
    'تحديد يدوي',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ExpenseProvider>().fetchExpenses();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('المصروفات'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: _showSearchDialog,
            ),
            IconButton(
              icon: const Icon(Icons.add_circle),
              onPressed: _addNewExpense,
            ),
          ],
        ),
        body: Consumer<ExpenseProvider>(
          builder: (BuildContext context, ExpenseProvider expenseProvider,
              Widget? child) {
            if (expenseProvider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.red),
              );
            }

            if (expenseProvider.error != null) {
              return _buildErrorWidget(expenseProvider.error!);
            }

            final List<Expense> expenses = expenseProvider.filteredExpenses;

            return Column(
              children: <Widget>[
                // شريط البحث والتصفية
                _buildSearchAndFilterBar(),

                // قائمة المصروفات
                Expanded(
                  child: expenses.isEmpty
                      ? _buildEmptyState()
                      : _buildExpensesList(expenses),
                ),
              ],
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _addNewExpense,
          backgroundColor: Colors.red,
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        children: <Widget>[
          // شريط البحث
          TextFormField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الوصف أو المبلغ...',
              prefixIcon: const Icon(Icons.search, color: Colors.red),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _applyFilters();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red),
              ),
            ),
            onChanged: (String value) => _applyFilters(),
          ),

          const SizedBox(height: 12),

          // عوامل التصفية
          Row(
            children: <Widget>[
              // تصفية الفئة
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategoryFilter,
                  decoration: InputDecoration(
                    labelText: 'الفئة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: _categoryFilters.map((String filter) {
                    return DropdownMenuItem(
                      value: filter,
                      child: Text(filter, style: const TextStyle(fontSize: 14)),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategoryFilter = value;
                      });
                      _applyFilters();
                    }
                  },
                ),
              ),

              const SizedBox(width: 12),

              // تصفية الفترة
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriodFilter,
                  decoration: InputDecoration(
                    labelText: 'الفترة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: _periodFilters.map((String filter) {
                    return DropdownMenuItem(
                      value: filter,
                      child: Text(filter, style: const TextStyle(fontSize: 14)),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedPeriodFilter = value;
                      });
                      if (value == 'تحديد يدوي') {
                        _showDateRangeDialog();
                      } else {
                        _applyFilters();
                      }
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExpensesList(List<Expense> expenses) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: expenses.length,
      itemBuilder: (BuildContext context, int index) {
        final Expense expense = expenses[index];
        return _buildExpenseCard(expense);
      },
    );
  }

  Widget _buildExpenseCard(Expense expense) {
    Color categoryColor = _getCategoryColor(expense.category);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        expense.description ?? 'مصروف غير محدد',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        expense.formattedDate,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      expense.formattedAmount,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: categoryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: categoryColor),
                      ),
                      child: Text(
                        expense.categoryDisplayName,
                        style: TextStyle(
                          fontSize: 12,
                          color: categoryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            if (expense.notes != null && expense.notes!.isNotEmpty) ...<Widget>[
              const SizedBox(height: 8),
              Text(
                expense.notes!,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            const SizedBox(height: 12),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                _buildActionButton(
                  icon: Icons.visibility,
                  label: 'عرض',
                  color: Colors.blue,
                  onPressed: () => _viewExpenseDetails(expense),
                ),
                _buildActionButton(
                  icon: Icons.edit,
                  label: 'تعديل',
                  color: Colors.orange,
                  onPressed: () => _editExpense(expense),
                ),
                _buildActionButton(
                  icon: Icons.delete,
                  label: 'حذف',
                  color: Colors.red,
                  onPressed: () => _deleteExpense(expense),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 16),
          label: Text(label, style: const TextStyle(fontSize: 12)),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.money_off_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مصروفات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة مصروف جديد',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addNewExpense,
            icon: const Icon(Icons.add),
            label: const Text('إضافة مصروف'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.read<ExpenseProvider>().fetchExpenses();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String? category) {
    switch (category) {
      case 'rent':
        return Colors.blue;
      case 'salaries':
        return Colors.green;
      case 'supplies':
        return Colors.orange;
      case 'utilities':
        return Colors.purple;
      case 'maintenance':
        return Colors.red;
      case 'marketing':
        return Colors.pink;
      case 'transportation':
        return Colors.teal;
      case 'miscellaneous':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _mapCategoryToEnglish(String arabicCategory) {
    switch (arabicCategory) {
      case 'إيجار':
        return 'rent';
      case 'رواتب':
        return 'salaries';
      case 'مستلزمات':
        return 'supplies';
      case 'خدمات':
        return 'utilities';
      case 'صيانة':
        return 'maintenance';
      case 'تسويق':
        return 'marketing';
      case 'نقل ومواصلات':
        return 'transportation';
      case 'متنوعة':
        return 'miscellaneous';
      default:
        return arabicCategory;
    }
  }

  void _applyFilters() {
    final ExpenseProvider provider = context.read<ExpenseProvider>();

    if (_selectedPeriodFilter != 'الكل' &&
        _selectedPeriodFilter != 'تحديد يدوي') {
      provider.filterByPeriod(_selectedPeriodFilter);
    } else {
      provider.filterExpenses(
        searchQuery: _searchController.text,
        category: _selectedCategoryFilter == 'الكل'
            ? null
            : _mapCategoryToEnglish(_selectedCategoryFilter),
        startDate: _startDate,
        endDate: _endDate,
      );
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('البحث والتصفية'),
        content: const Text('استخدم شريط البحث أعلى الشاشة للبحث في المصروفات'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDateRangeDialog() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _applyFilters();
    }
  }

  void _addNewExpense() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => const CreateExpenseScreen(),
      ),
    );
  }

  void _viewExpenseDetails(Expense expense) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) =>
            ExpenseDetailsScreen(expense: expense),
      ),
    );
  }

  void _editExpense(Expense expense) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) =>
            CreateExpenseScreen(expense: expense),
      ),
    );
  }

  Future<void> _deleteExpense(Expense expense) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
            'هل أنت متأكد من حذف مصروف "${expense.description}"؟\nلا يمكن التراجع عن هذا الإجراء.'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await context.read<ExpenseProvider>().deleteExpense(expense.id!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف المصروف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('فشل في حذف المصروف: $e')),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
