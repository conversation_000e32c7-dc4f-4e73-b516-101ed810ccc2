import 'package:flutter/material.dart';
import 'package:inventory_management_app/config/app_dimensions.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../widgets/custom_buttons.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/validators.dart';
import '../../utils/snackbar_helper.dart';
import '../../providers/customer_provider.dart';
import '../../models/customer.dart';

/// شاشة إضافة/تعديل العميل
class CustomerFormScreen extends StatefulWidget {
  final String? customerId;
  final bool isEditing;

  const CustomerFormScreen({
    super.key,
    this.customerId,
    this.isEditing = false,
  });

  @override
  State<CustomerFormScreen> createState() => _CustomerFormScreenState();
}

class _CustomerFormScreenState extends State<CustomerFormScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _balanceController = TextEditingController();

  bool _isLoading = false;
  Customer? _existingCustomer;

  @override
  void initState() {
    super.initState();
    if (widget.isEditing && widget.customerId != null) {
      _loadCustomer();
    } else {
      _balanceController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  void _loadCustomer() async {
    setState(() => _isLoading = true);

    try {
      final CustomerProvider customerProvider =
          context.read<CustomerProvider>();
      final Customer? customer =
          customerProvider.customers.cast<Customer?>().firstWhere(
                (Customer? c) => c?.id.toString() == widget.customerId,
                orElse: () => null,
              );

      if (customer != null) {
        _existingCustomer = customer;
        _populateForm(customer);
      }
    } catch (e) {
      SnackBarHelper.showError(context, 'فشل في تحميل بيانات العميل');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _populateForm(Customer customer) {
    _nameController.text = customer.name ?? '';
    _phoneController.text = customer.phone ?? '';
    _emailController.text = customer.email ?? '';
    _addressController.text = customer.address ?? '';
    _notesController.text = customer.notes ?? '';
    _balanceController.text = (customer.balance ?? 0).toString();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildForm(),
        bottomNavigationBar: _buildBottomActions(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.isEditing ? 'تعديل العميل' : 'إضافة عميل جديد'),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      actions: <Widget>[
        if (widget.isEditing)
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteCustomer,
          ),
      ],
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildSectionTitle('المعلومات الأساسية'),
            _buildBasicInfoSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle('معلومات الاتصال'),
            _buildContactInfoSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle('المعلومات المالية'),
            _buildFinancialInfoSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle('ملاحظات إضافية'),
            _buildNotesSection(),
            const SizedBox(height: AppDimensions.paddingXL),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Text(
        title,
        style: AppStyles.titleMedium.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العميل *',
                prefixIcon: Icon(Icons.person),
                helperText: 'أدخل الاسم الكامل للعميل',
              ),
              validator: Validators.validateName,
              textInputAction: TextInputAction.next,
              textCapitalization: TextCapitalization.words,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                prefixIcon: Icon(Icons.phone),
                helperText: 'مثال: 0501234567',
              ),
              keyboardType: TextInputType.phone,
              validator: Validators.validatePhone,
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                prefixIcon: Icon(Icons.email),
                helperText: 'اختياري',
              ),
              keyboardType: TextInputType.emailAddress,
              validator: Validators.validateEmail,
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'العنوان',
                prefixIcon: Icon(Icons.location_on),
                helperText: 'اختياري',
              ),
              validator: Validators.validateAddress,
              maxLines: 2,
              textInputAction: TextInputAction.next,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            TextFormField(
              controller: _balanceController,
              decoration: const InputDecoration(
                labelText: 'الرصيد الحالي',
                prefixIcon: Icon(Icons.account_balance_wallet),
                suffixText: 'ر.س',
                helperText: 'الرصيد الحالي للعميل (موجب = رصيد، سالب = دين)',
              ),
              keyboardType: TextInputType.number,
              validator: (String? value) {
                if (value == null || value.isEmpty) return null;
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                return null;
              },
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildBalanceInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceInfo() {
    final double balance = double.tryParse(_balanceController.text) ?? 0;

    Color color;
    String status;
    IconData icon;

    if (balance < 0) {
      color = AppColors.error;
      status = 'العميل مديون بمبلغ ${balance.abs().toStringAsFixed(2)} ر.س';
      icon = Icons.trending_down;
    } else if (balance > 0) {
      color = AppColors.success;
      status = 'العميل له رصيد ${balance.toStringAsFixed(2)} ر.س';
      icon = Icons.trending_up;
    } else {
      color = AppColors.textSecondary;
      status = 'الحساب متوازن';
      icon = Icons.balance;
    }

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        children: <Widget>[
          Icon(icon, color: color, size: 16),
          const SizedBox(width: AppDimensions.paddingS),
          Expanded(
            child: Text(
              status,
              style: AppStyles.bodySmall.copyWith(color: color),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            prefixIcon: Icon(Icons.note),
            helperText: 'أي ملاحظات إضافية عن العميل',
            border: InputBorder.none,
          ),
          maxLines: 4,
          validator: Validators.validateDescription,
          textInputAction: TextInputAction.done,
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: <Widget>[
            Expanded(
              child: CustomSecondaryButton(
                text: 'إلغاء',
                onPressed: _handleCancel,
                isFullWidth: true,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              flex: 2,
              child: CustomPrimaryButton(
                text: widget.isEditing ? 'حفظ التغييرات' : 'إضافة العميل',
                onPressed: _saveCustomer,
                icon: widget.isEditing ? Icons.save : Icons.person_add,
                isLoading: _isLoading,
                isFullWidth: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleCancel() async {
    if (_hasUnsavedChanges()) {
      final bool? confirmed = await EnhancedConfirmationDialog.show(
        context,
        title: 'تجاهل التغييرات؟',
        message: 'هل تريد تجاهل التغييرات غير المحفوظة؟',
        icon: Icons.warning,
      );

      if (confirmed == true) {
        context.pop();
      }
    } else {
      context.pop();
    }
  }

  bool _hasUnsavedChanges() {
    if (!widget.isEditing) {
      return _nameController.text.isNotEmpty ||
          _phoneController.text.isNotEmpty ||
          _emailController.text.isNotEmpty ||
          _addressController.text.isNotEmpty ||
          _notesController.text.isNotEmpty ||
          _balanceController.text != '0';
    }

    if (_existingCustomer == null) return false;

    return _nameController.text != (_existingCustomer!.name ?? '') ||
        _phoneController.text != (_existingCustomer!.phone ?? '') ||
        _emailController.text != (_existingCustomer!.email ?? '') ||
        _addressController.text != (_existingCustomer!.address ?? '') ||
        _notesController.text != (_existingCustomer!.notes ?? '') ||
        _balanceController.text != (_existingCustomer!.balance ?? 0).toString();
  }

  void _saveCustomer() async {
    if (!_formKey.currentState!.validate()) {
      SnackBarHelper.showError(context, 'يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final Customer customer = Customer(
        id: widget.isEditing ? _existingCustomer?.id : null,
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        balance: double.tryParse(_balanceController.text) ?? 0,
        createdAt:
            widget.isEditing ? _existingCustomer?.createdAt : DateTime.now(),
      );

      final CustomerProvider customerProvider =
          context.read<CustomerProvider>();

      if (widget.isEditing) {
        await customerProvider.updateCustomer(customer);
        if (mounted) {
          SnackBarHelper.showSuccess(context, 'تم تحديث العميل بنجاح');
        }
      } else {
        await customerProvider.addCustomer(customer);
        if (mounted) {
          SnackBarHelper.showSuccess(context, 'تم إضافة العميل بنجاح');
        }
      }

      if (mounted) {
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showError(
          context,
          widget.isEditing
              ? 'فشل في تحديث العميل: $e'
              : 'فشل في إضافة العميل: $e',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteCustomer() async {
    if (_existingCustomer == null) return;

    final CustomerProvider customerProvider = context.read<CustomerProvider>();

    final bool? confirmed = await EnhancedConfirmationDialog.showDelete(
      context,
      itemName: _existingCustomer!.name,
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      try {
        await customerProvider.deleteCustomer(_existingCustomer!.id!);
        if (mounted) {
          SnackBarHelper.showSuccess(context, 'تم حذف العميل بنجاح');
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          SnackBarHelper.showError(context, 'فشل في حذف العميل: $e');
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }
}
