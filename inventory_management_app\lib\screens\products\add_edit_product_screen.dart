import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/category.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/category_provider.dart';
import 'package:inventory_management_app/providers/validation_provider.dart';
import 'package:inventory_management_app/widgets/custom_app_bar.dart';
import 'package:inventory_management_app/widgets/custom_form_field.dart';

/// Screen for adding or editing a product
class AddEditProductScreen extends StatefulWidget {
  /// The ID of the product to edit, null for adding new product
  final int? productId;

  /// Constructor for AddEditProductScreen
  const AddEditProductScreen({super.key, this.productId});

  @override
  State<AddEditProductScreen> createState() => _AddEditProductScreenState();
}

class _AddEditProductScreenState extends State<AddEditProductScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _barcodeController = TextEditingController();

  int? _selectedCategoryId;
  bool _isLoading = false;
  Product? _existingProduct;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CategoryProvider>().fetchCategories();
      if (widget.productId != null) {
        _loadExistingProduct();
      }
    });
  }

  Future<void> _loadExistingProduct() async {
    setState(() => _isLoading = true);

    final ProductProvider productProvider = context.read<ProductProvider>();
    final List<Product> products = productProvider.products;
    _existingProduct =
        products.firstWhere((Product p) => p.id == widget.productId);

    if (_existingProduct != null) {
      _nameController.text = _existingProduct!.name;
      _descriptionController.text = _existingProduct!.description ?? '';
      _priceController.text = _existingProduct!.price.toString() ?? '';
      _quantityController.text = _existingProduct!.quantity?.toString() ?? '';
      // _barcodeController.text = _existingProduct!.barcode ?? '';
      // FIX: Use the correct property name if 'barcode' does not exist in Product
      // For example, if the correct property is 'sku', use:
      // _barcodeController.text = _existingProduct!.sku ?? '';
      // Otherwise, define 'barcode' in the Product model.
      _selectedCategoryId = _existingProduct!.categoryId;
    }

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    final bool isEditing = widget.productId != null;

    return Scaffold(
      appBar: CustomAppBar(
        title: isEditing ? 'Edit Product' : 'Add Product',
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveProduct,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    CustomFormField(
                      label: 'Product Name *',
                      controller: _nameController,
                      validator: (String? value) => context
                          .read<ValidationProvider>()
                          .validateRequired(value, 'Product Name'),
                      prefixIcon: const Icon(Icons.inventory),
                    ),
                    CustomFormField(
                      label: 'Description',
                      controller: _descriptionController,
                      maxLines: 3,
                      prefixIcon: const Icon(Icons.description),
                    ),
                    Consumer<CategoryProvider>(
                      builder: (BuildContext context,
                          CategoryProvider categoryProvider, Widget? child) {
                        return DropdownButtonFormField<int>(
                          value: _selectedCategoryId,
                          decoration: InputDecoration(
                            labelText: 'Category',
                            prefixIcon: const Icon(Icons.category),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12)),
                          ),
                          items: categoryProvider.categories
                              .map((Category category) {
                            return DropdownMenuItem<int>(
                              value: category.id,
                              child: Text(category.name),
                            );
                          }).toList(),
                          onChanged: (int? value) =>
                              setState(() => _selectedCategoryId = value),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    NumberFormField(
                      label: 'Price *',
                      controller: _priceController,
                      validator: (String? value) => context
                          .read<ValidationProvider>()
                          .validatePositiveNumber(value, 'Price'),
                      allowDecimals: true,
                      min: 0.01,
                    ),
                    NumberFormField(
                      label: 'Quantity *',
                      controller: _quantityController,
                      validator: (String? value) => context
                          .read<ValidationProvider>()
                          .validateNonNegativeNumber(value, 'Quantity'),
                      allowDecimals: true,
                      min: 0,
                    ),
                    CustomFormField(
                      label: 'Barcode',
                      controller: _barcodeController,
                      prefixIcon: const Icon(Icons.qr_code),
                    ),
                    const SizedBox(height: 24),
                    Consumer<ProductProvider>(
                      builder: (BuildContext context,
                          ProductProvider productProvider, Widget? child) {
                        return ElevatedButton(
                          onPressed:
                              productProvider.isLoading ? null : _saveProduct,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                          ),
                          child: productProvider.isLoading
                              ? const CircularProgressIndicator()
                              : Text(
                                  isEditing ? 'Update Product' : 'Add Product'),
                        );
                      },
                    ),
                    if (context
                        .watch<ValidationProvider>()
                        .hasValidationErrors) ...<Widget>[
                      const SizedBox(height: 16),
                      Card(
                        color: Colors.red.shade50,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              const Text('Please fix the following errors:',
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red)),
                              const SizedBox(height: 8),
                              ...context
                                  .watch<ValidationProvider>()
                                  .getAllValidationErrors()
                                  .map(
                                    (String error) => Text('• $error',
                                        style:
                                            const TextStyle(color: Colors.red)),
                                  ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    final ValidationProvider validationProvider =
        context.read<ValidationProvider>();
    final ProductProvider productProvider = context.read<ProductProvider>();

    validationProvider.clearValidationErrors();

    // Validate product name uniqueness
    final String? nameError =
        await validationProvider.validateProductNameUniqueness(
      _nameController.text,
      _selectedCategoryId,
      excludeId: widget.productId,
    );

    if (nameError != null) return;

    final Product product = Product(
      id: widget.productId,
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      categoryId: _selectedCategoryId,
      price: double.tryParse(_priceController.text) ?? 0.0,
      quantity: double.tryParse(_quantityController.text),
      barcode: _barcodeController.text.trim().isEmpty
          ? null
          : _barcodeController.text.trim(),
    );

    try {
      if (widget.productId != null) {
        await productProvider.updateProduct(product);
      } else {
        await productProvider.addProduct(product);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.productId != null
                ? 'Product updated successfully'
                : 'Product added successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _barcodeController.dispose();
    super.dispose();
  }
}
