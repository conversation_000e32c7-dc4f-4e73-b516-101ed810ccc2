import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../config/app_colors.dart';
import '../widgets/dashboard_widgets.dart';
import '../providers/product_provider.dart';
import '../providers/sale_provider.dart';
import '../providers/purchase_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/supplier_provider.dart';

/// قسم الإحصائيات مع البطاقات الأربع
class StatsSection extends StatelessWidget {
  const StatsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
          child: Text(
            'الإحصائيات',
            style: TextStyle(
              fontFamily: '<PERSON><PERSON><PERSON>',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Consumer5<ProductProvider, SaleProvider, PurchaseProvider,
              CustomerProvider, SupplierProvider>(
            builder: (BuildContext context,
                ProductProvider productProvider,
                SaleProvider saleProvider,
                PurchaseProvider purchaseProvider,
                CustomerProvider customerProvider,
                SupplierProvider supplierProvider,
                Widget? child) {
              return GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                children: <Widget>[
                  StatCard(
                    title: 'إجمالي المنتجات',
                    value: '${productProvider.products.length}',
                    icon: Icons.inventory_2,
                    color: AppColors.primary,
                    subtitle: 'منتج متاح',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/products');
                    },
                    isLoading: productProvider.isLoading,
                  ),
                  StatCard(
                    title: 'العملاء',
                    value: '${customerProvider.customers.length}',
                    icon: Icons.people,
                    color: AppColors.secondary,
                    subtitle: 'عميل مسجل',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/customers');
                    },
                    isLoading: customerProvider.isLoading,
                  ),
                  StatCard(
                    title: 'المبيعات اليوم',
                    value: '${saleProvider.sales.length}',
                    icon: Icons.point_of_sale,
                    color: AppColors.accent,
                    subtitle: 'عملية بيع',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/sales');
                    },
                    isLoading: saleProvider.isLoading,
                  ),
                  StatCard(
                    title: 'الموردين',
                    value: '${supplierProvider.suppliers.length}',
                    icon: Icons.local_shipping,
                    color: AppColors.info,
                    subtitle: 'مورد نشط',
                    onTap: () {
                      Navigator.of(context).pop(); // إغلاق الـ drawer
                      context.go('/suppliers');
                    },
                    isLoading: supplierProvider.isLoading,
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
