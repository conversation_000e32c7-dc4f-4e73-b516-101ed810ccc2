import 'package:sqflite/sqflite.dart';
import '../models/product.dart';
import 'database_service.dart';

/// Service class for handling Product CRUD operations
class ProductService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all products from the database
  Future<List<Product>> getAllProducts() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('products');

    return List.generate(maps.length, (int i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get a product by its ID
  Future<Product?> getProductById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new product into the database
  Future<int> insertProduct(Product product) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'products',
      product.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing product in the database
  Future<int> updateProduct(Product product) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'products',
      product.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[product.id],
    );
  }

  /// Delete a product from the database
  Future<int> deleteProduct(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'products',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Search products by name
  Future<List<Product>> searchProductsByName(String name) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'name LIKE ?',
      whereArgs: <Object?>['%$name%'],
    );

    return List.generate(maps.length, (int i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get products by category
  Future<List<Product>> getProductsByCategory(int categoryId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'categoryId = ?',
      whereArgs: <Object?>[categoryId],
    );

    return List.generate(maps.length, (int i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get products by supplier
  Future<List<Product>> getProductsBySupplier(int supplierId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'supplierId = ?',
      whereArgs: <Object?>[supplierId],
    );

    return List.generate(maps.length, (int i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get products with low stock (quantity below threshold)
  Future<List<Product>> getLowStockProducts(double threshold) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'quantity < ?',
      whereArgs: <Object?>[threshold],
    );

    return List.generate(maps.length, (int i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Add a new product (alias for insertProduct)
  Future<int> addProduct(Product product) async {
    return await insertProduct(product);
  }

  /// Get product by barcode
  Future<Product?> getProductByBarcode(String barcode) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'barcode = ?',
      whereArgs: <Object?>[barcode],
    );

    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }
    return null;
  }

  /// Update product quantity (warehouse and store)
  Future<int> updateProductQuantity(
      int productId, int warehouseQuantity, int storeQuantity) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'products',
      <String, Object?>{
        'warehouseQuantity': warehouseQuantity,
        'storeQuantity': storeQuantity,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: <Object?>[productId],
    );
  }

  /// Get total number of products
  Future<int> getProductCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result =
        await db.rawQuery('SELECT COUNT(*) as count FROM products');
    return result.first['count'] as int;
  }
}
