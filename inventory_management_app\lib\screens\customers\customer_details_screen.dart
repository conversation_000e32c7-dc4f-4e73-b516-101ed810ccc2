import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/models/customer.dart';
import '../../utils/navigation_manager.dart';

class CustomerDetailsScreen extends StatefulWidget {
  final int? customerId;
  final bool isReadOnly;

  const CustomerDetailsScreen({
    super.key,
    this.customerId,
    this.isReadOnly = false,
  });

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late String _name;
  late String _address;
  late String _phone;
  late String _email;
  Customer? _customer;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCustomer();
  }

  void _loadCustomer() {
    if (widget.customerId != null) {
      setState(() {
        _isLoading = true;
      });

      final CustomerProvider customerProvider =
          context.read<CustomerProvider>();
      try {
        _customer = customerProvider.customers
            .firstWhere((Customer c) => c.id == widget.customerId);
      } catch (e) {
        _customer = null;
      }

      _name = _customer?.name ?? '';
      _address = _customer?.address ?? '';
      _phone = _customer?.phone ?? '';
      _email = _customer?.email ?? '';

      setState(() {
        _isLoading = false;
      });
    } else {
      _name = '';
      _address = '';
      _phone = '';
      _email = '';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return BackButtonHandler(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Scaffold(
          appBar: AppBar(
            title: Text(widget.customerId == null
                ? 'إضافة عميل جديد'
                : widget.isReadOnly
                    ? 'تفاصيل العميل'
                    : 'تعديل العميل'),
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                children: <Widget>[
                  TextFormField(
                    initialValue: _name,
                    readOnly: widget.isReadOnly,
                    decoration: const InputDecoration(labelText: 'اسم العميل'),
                    validator: (String? value) {
                      if (!widget.isReadOnly &&
                          (value == null || value.isEmpty)) {
                        return 'يرجى إدخال اسم العميل';
                      }
                      return null;
                    },
                    onSaved: (String? value) => _name = value!,
                  ),
                  TextFormField(
                    initialValue: _address,
                    readOnly: widget.isReadOnly,
                    decoration: const InputDecoration(labelText: 'العنوان'),
                    onSaved: (String? value) => _address = value!,
                  ),
                  TextFormField(
                    initialValue: _phone,
                    readOnly: widget.isReadOnly,
                    decoration: const InputDecoration(labelText: 'رقم الهاتف'),
                    keyboardType: TextInputType.phone,
                    onSaved: (String? value) => _phone = value!,
                  ),
                  TextFormField(
                    initialValue: _email,
                    readOnly: widget.isReadOnly,
                    decoration:
                        const InputDecoration(labelText: 'البريد الإلكتروني'),
                    keyboardType: TextInputType.emailAddress,
                    onSaved: (String? value) => _email = value!,
                  ),
                  if (!widget.isReadOnly)
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          _formKey.currentState!.save();
                          final Customer customer = Customer(
                            id: _customer?.id,
                            name: _name,
                            address: _address,
                            phone: _phone,
                            email: _email,
                          );
                          final CustomerProvider customerProvider =
                              Provider.of<CustomerProvider>(context,
                                  listen: false);
                          if (widget.customerId == null) {
                            customerProvider.addCustomer(customer);
                          } else {
                            customerProvider.updateCustomer(customer);
                          }
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(widget.customerId == null ? 'إضافة' : 'حفظ'),
                    ),
                ],
              ),
            ),
        ),
      ),
    );
  }
}
