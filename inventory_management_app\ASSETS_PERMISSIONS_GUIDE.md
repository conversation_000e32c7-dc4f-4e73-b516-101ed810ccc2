# دليل الموارد والصلاحيات - أسامة ماركت

## 🎨 **إدارة الموارد (Assets)**

### **📁 بنية مجلدات الموارد:**
```
assets/
├── images/          # الصور والرسوم
├── icons/           # الأيقونات والرموز  
├── data/            # البيانات النموذجية
├── samples/         # ملفات النماذج والقوالب
└── fonts/           # الخطوط المخصصة
```

### **🖼️ استخدام الصور والأيقونات:**

#### **تحميل صورة:**
```dart
// صورة بسيطة
Widget logo = AssetsHelper.getImage('logo.png');

// صورة مع خصائص
Widget banner = AssetsHelper.getImage(
  'banner.jpg',
  width: 200,
  height: 100,
  fit: BoxFit.cover,
);
```

#### **تحميل أيقونة:**
```dart
// أيقونة بسيطة
Widget salesIcon = AssetsHelper.getIcon('sales_icon.png');

// أيقونة مع خصائص
Widget menuIcon = AssetsHelper.getIcon(
  'menu_icon.png',
  size: 32,
  color: Colors.blue,
);

// أيقونة كـ ImageIcon
ImageIcon navIcon = AssetsHelper.getImageIcon('nav_icon.png');
```

### **📊 استخدام البيانات النموذجية:**

#### **تحميل منتجات نموذجية:**
```dart
// تحميل جميع البيانات
Map<String, dynamic> data = await AssetsHelper.loadSampleProducts();
List<dynamic> products = data['products'];

// الحصول على قوائم جاهزة
List<String> categories = await AssetsHelper.getProductCategories();
List<String> units = await AssetsHelper.getProductUnits();
```

#### **تحميل عملاء نموذجيين:**
```dart
// تحميل بيانات العملاء
Map<String, dynamic> data = await AssetsHelper.loadSampleCustomers();
List<dynamic> customers = data['customers'];

// الحصول على قوائم مساعدة
List<String> districts = await AssetsHelper.getCustomerDistricts();
List<String> paymentMethods = await AssetsHelper.getPaymentMethods();
```

### **🔍 فحص وجود الملفات:**
```dart
// فحص وجود صورة
bool exists = await AssetsHelper.imageExists('logo.png');

// فحص وجود أيقونة
bool iconExists = await AssetsHelper.iconExists('menu_icon.png');

// فحص أي ملف
bool fileExists = await AssetsHelper.assetExists('assets/data/config.json');
```

## 🌐 **نظام الترجمة (Localization)**

### **📝 إضافة نصوص جديدة:**

#### **في ملف app_ar.arb:**
```json
{
  "newFeature": "ميزة جديدة",
  "@newFeature": {
    "description": "وصف الميزة الجديدة"
  }
}
```

#### **في ملف app_en.arb:**
```json
{
  "newFeature": "New Feature",
  "@newFeature": {
    "description": "Description of the new feature"
  }
}
```

### **🔤 استخدام النصوص المترجمة:**
```dart
// في أي Widget
Text(AppLocalizations.of(context)!.appTitle)
Text(AppLocalizations.of(context)!.sales)
Text(AppLocalizations.of(context)!.newFeature)

// مع متغيرات
Text(AppLocalizations.of(context)!.welcomeUser(userName))
```

### **⚙️ إعداد الترجمة في التطبيق:**
```dart
// في main.dart
MaterialApp(
  localizationsDelegates: AppLocalizations.localizationsDelegates,
  supportedLocales: AppLocalizations.supportedLocales,
  locale: const Locale('ar'), // اللغة الافتراضية
  // باقي الإعدادات...
)
```

## 🔐 **إدارة الصلاحيات (Permissions)**

### **📱 الصلاحيات المدعومة:**
- **التخزين** - لحفظ الملفات والنسخ الاحتياطية
- **الكاميرا** - لمسح الباركود والتصوير
- **الموقع** - لتحديد موقع المتجر والعملاء
- **جهات الاتصال** - لاستيراد بيانات العملاء
- **الإشعارات** - للتنبيهات والتذكيرات

### **🎯 طلب صلاحية واحدة:**

#### **طريقة بسيطة:**
```dart
// طلب صلاحية التخزين
bool granted = await PermissionService.requestStoragePermission();
if (granted) {
  // تنفيذ العملية
  await saveFile();
} else {
  // إظهار رسالة خطأ
  showErrorMessage();
}
```

#### **طريقة مع واجهة مستخدم:**
```dart
// طلب صلاحية مع حوار توضيحي
bool granted = await PermissionHelper.requestStorageWithDialog(context);
if (granted) {
  // تنفيذ العملية
  await saveBackup();
}
```

### **📋 طلب صلاحيات متعددة:**

#### **الصلاحيات الأساسية:**
```dart
// طلب الصلاحيات المطلوبة للتشغيل الأساسي
Map<String, bool> results = await PermissionService.requestEssentialPermissions();

// التحقق من النتائج
if (results['storage'] == true) {
  // يمكن حفظ الملفات
}
if (results['notification'] == true) {
  // يمكن إرسال الإشعارات
}
```

#### **الصلاحيات الاختيارية:**
```dart
// طلب الصلاحيات التي تحسن التجربة
Map<String, bool> optional = await PermissionService.requestOptionalPermissions();

if (optional['camera'] == true) {
  // يمكن مسح الباركود
}
if (optional['location'] == true) {
  // يمكن تحديد الموقع
}
```

### **🔍 فحص الصلاحيات:**

#### **فحص صلاحية واحدة:**
```dart
// فحص بدون طلب
bool hasStorage = await PermissionService.checkStoragePermission();
bool hasCamera = await PermissionService.checkCameraPermission();
```

#### **فحص جميع الصلاحيات:**
```dart
// فحص شامل
Map<String, bool> allPermissions = await PermissionService.checkAllPermissions();

// عرض تقرير للمستخدم
await PermissionHelper.showPermissionsStatus(context);
```

### **⚙️ إعدادات التطبيق:**
```dart
// فتح إعدادات التطبيق لمنح الصلاحيات يدوياً
await PermissionService.openAppSettings();
```

## 🛠️ **أمثلة عملية**

### **📸 مسح الباركود:**
```dart
Future<void> scanBarcode() async {
  // طلب صلاحية الكاميرا
  final bool hasPermission = await PermissionHelper.requestCameraWithDialog(context);
  
  if (hasPermission) {
    // تشغيل ماسح الباركود
    final String? barcode = await BarcodeScanner.scan();
    if (barcode != null) {
      // البحث عن المنتج
      await searchProductByBarcode(barcode);
    }
  } else {
    // إظهار رسالة خطأ
    SnackBarHelper.showError(context, 'صلاحية الكاميرا مطلوبة لمسح الباركود');
  }
}
```

### **💾 حفظ نسخة احتياطية:**
```dart
Future<void> createBackup() async {
  // طلب صلاحية التخزين
  final bool hasPermission = await PermissionHelper.requestStorageWithDialog(context);
  
  if (hasPermission) {
    // إنشاء النسخة الاحتياطية
    await BackupService.createBackup();
    SnackBarHelper.showSuccess(context, 'تم إنشاء النسخة الاحتياطية بنجاح');
  }
}
```

### **📱 استيراد جهات الاتصال:**
```dart
Future<void> importContacts() async {
  // طلب صلاحية جهات الاتصال
  final bool hasPermission = await PermissionHelper.requestContactsWithDialog(context);
  
  if (hasPermission) {
    // استيراد جهات الاتصال
    final List<Contact> contacts = await ContactsService.getContacts();
    await CustomerService.importFromContacts(contacts);
  }
}
```

## 🔧 **نصائح للتطوير**

### **📦 إضافة موارد جديدة:**
1. ضع الملف في المجلد المناسب
2. تأكد من تحديث pubspec.yaml إذا لزم الأمر
3. استخدم AssetsHelper للوصول للملف
4. اختبر وجود الملف قبل الاستخدام

### **🔐 إضافة صلاحية جديدة:**
1. أضف الصلاحية في AndroidManifest.xml
2. أضف طريقة في PermissionService
3. أضف طريقة مع واجهة في PermissionHelper
4. اختبر على جميع المنصات

### **🌐 إضافة ترجمة جديدة:**
1. أضف النص في ملفات ARB
2. شغل `flutter gen-l10n`
3. استخدم النص في الكود
4. اختبر مع اللغات المختلفة

## 🚨 **استكشاف الأخطاء**

### **مشكلة: الصورة لا تظهر**
- تحقق من وجود الملف في المجلد الصحيح
- تأكد من إضافة المسار في pubspec.yaml
- استخدم AssetsHelper.imageExists() للفحص

### **مشكلة: الصلاحية مرفوضة**
- تحقق من إضافة الصلاحية في AndroidManifest.xml
- استخدم PermissionHelper مع الحوارات التوضيحية
- وجه المستخدم لإعدادات التطبيق

### **مشكلة: الترجمة لا تعمل**
- تأكد من تشغيل `flutter gen-l10n`
- تحقق من إعداد localizationsDelegates
- تأكد من وجود النص في ملفات ARB

---

**تذكر:** استخدم دائماً الطرق الموحدة لضمان الاتساق والجودة! 🎯✨
