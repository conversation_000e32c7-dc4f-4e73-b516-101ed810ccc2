import 'package:flutter/material.dart';
import '../models/product.dart';
import '../services/product_service.dart';

/// مزود التحقق من صحة البيانات
class ValidationProvider with ChangeNotifier {
  final ProductService _productService = ProductService();
  
  // قائمة أخطاء التحقق
  final List<String> _validationErrors = <String>[];
  
  /// الحصول على جميع أخطاء التحقق
  List<String> getAllValidationErrors() => List.unmodifiable(_validationErrors);
  
  /// التحقق من وجود أخطاء تحقق
  bool get hasValidationErrors => _validationErrors.isNotEmpty;
  
  /// مسح جميع أخطاء التحقق
  void clearValidationErrors() {
    _validationErrors.clear();
    notifyListeners();
  }
  
  /// إضافة خطأ تحقق
  void addValidationError(String error) {
    if (!_validationErrors.contains(error)) {
      _validationErrors.add(error);
      notifyListeners();
    }
  }
  
  /// إزالة خطأ تحقق محدد
  void removeValidationError(String error) {
    _validationErrors.remove(error);
    notifyListeners();
  }
  
  /// التحقق من الحقول المطلوبة
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  /// التحقق من الأرقام الموجبة
  String? validatePositiveNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    final double? number = double.tryParse(value);
    if (number == null) {
      return '$fieldName must be a valid number';
    }
    
    if (number <= 0) {
      return '$fieldName must be greater than 0';
    }
    
    return null;
  }
  
  /// التحقق من الأرقام غير السالبة
  String? validateNonNegativeNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    final double? number = double.tryParse(value);
    if (number == null) {
      return '$fieldName must be a valid number';
    }
    
    if (number < 0) {
      return '$fieldName cannot be negative';
    }
    
    return null;
  }
  
  /// التحقق من صحة البريد الإلكتروني
  String? validateEmail(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    final RegExp emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }
  
  /// التحقق من رقم الهاتف
  String? validatePhoneNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    // تحقق بسيط من رقم الهاتف السعودي
    final RegExp phoneRegex = RegExp(r'^(05|5)[0-9]{8}$');
    
    if (!phoneRegex.hasMatch(value.replaceAll(' ', '').replaceAll('-', ''))) {
      return 'Please enter a valid Saudi phone number';
    }
    
    return null;
  }
  
  /// التحقق من الحد الأدنى لطول النص
  String? validateMinLength(String? value, String fieldName, int minLength) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    if (value.length < minLength) {
      return '$fieldName must be at least $minLength characters long';
    }
    
    return null;
  }
  
  /// التحقق من الحد الأقصى لطول النص
  String? validateMaxLength(String? value, String fieldName, int maxLength) {
    if (value != null && value.length > maxLength) {
      return '$fieldName cannot exceed $maxLength characters';
    }
    
    return null;
  }
  
  /// التحقق من تطابق كلمات المرور
  String? validatePasswordMatch(String? password, String? confirmPassword) {
    if (password != confirmPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }
  
  /// التحقق من قوة كلمة المرور
  String? validatePasswordStrength(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    if (value.length < 8) {
      return '$fieldName must be at least 8 characters long';
    }
    
    // التحقق من وجود حرف كبير وصغير ورقم
    final bool hasUppercase = value.contains(RegExp(r'[A-Z]'));
    final bool hasLowercase = value.contains(RegExp(r'[a-z]'));
    final bool hasDigits = value.contains(RegExp(r'[0-9]'));
    
    if (!hasUppercase || !hasLowercase || !hasDigits) {
      return '$fieldName must contain uppercase, lowercase, and numbers';
    }
    
    return null;
  }
  
  /// التحقق من تفرد اسم المنتج
  Future<String?> validateProductNameUniqueness(
    String name,
    int? categoryId, {
    int? excludeId,
  }) async {
    try {
      final List<Product> products = await _productService.getAllProducts();
      
      final bool nameExists = products.any((Product product) =>
          product.name.toLowerCase() == name.toLowerCase() &&
          product.categoryId == categoryId &&
          product.id != excludeId);
      
      if (nameExists) {
        const String error = 'Product name already exists in this category';
        addValidationError(error);
        return error;
      }
      
      return null;
    } catch (e) {
      const String error = 'Failed to validate product name uniqueness';
      addValidationError(error);
      return error;
    }
  }
  
  /// التحقق من صحة الباركود
  String? validateBarcode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // الباركود اختياري
    }
    
    // تحقق بسيط من طول الباركود
    if (value.length < 8 || value.length > 13) {
      return 'Barcode must be between 8 and 13 characters';
    }
    
    // تحقق من أن الباركود يحتوي على أرقام فقط
    if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      return 'Barcode must contain only numbers';
    }
    
    return null;
  }
  
  /// التحقق من صحة السعر
  String? validatePrice(String? value, {double? minPrice, double? maxPrice}) {
    if (value == null || value.trim().isEmpty) {
      return 'Price is required';
    }
    
    final double? price = double.tryParse(value);
    if (price == null) {
      return 'Please enter a valid price';
    }
    
    if (price <= 0) {
      return 'Price must be greater than 0';
    }
    
    if (minPrice != null && price < minPrice) {
      return 'Price must be at least $minPrice';
    }
    
    if (maxPrice != null && price > maxPrice) {
      return 'Price cannot exceed $maxPrice';
    }
    
    return null;
  }
  
  /// التحقق من صحة الكمية
  String? validateQuantity(String? value, {double? minQuantity}) {
    if (value == null || value.trim().isEmpty) {
      return 'Quantity is required';
    }
    
    final double? quantity = double.tryParse(value);
    if (quantity == null) {
      return 'Please enter a valid quantity';
    }
    
    if (quantity < 0) {
      return 'Quantity cannot be negative';
    }
    
    if (minQuantity != null && quantity < minQuantity) {
      return 'Quantity must be at least $minQuantity';
    }
    
    return null;
  }
  
  /// التحقق من صحة النسبة المئوية
  String? validatePercentage(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    final double? percentage = double.tryParse(value);
    if (percentage == null) {
      return '$fieldName must be a valid number';
    }
    
    if (percentage < 0 || percentage > 100) {
      return '$fieldName must be between 0 and 100';
    }
    
    return null;
  }
}
