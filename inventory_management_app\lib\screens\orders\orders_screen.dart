import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:inventory_management_app/models/order.dart';
import 'package:inventory_management_app/screens/orders/order_details_screen.dart';

class OrdersScreen extends StatelessWidget {
  const OrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final OrderProvider orderProvider = Provider.of<OrderProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders'),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: orderProvider.orders.length,
        itemBuilder: (BuildContext context, int index) {
          final Order order = orderProvider.orders[index];
          return Card(
            child: ListTile(
              title: Text('Order ID: ${order.id}'),
              subtitle: Text('Customer ID: ${order.customerId}'),
              trailing: const Icon(Icons.arrow_forward),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        OrderDetailsScreen(order: order),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
