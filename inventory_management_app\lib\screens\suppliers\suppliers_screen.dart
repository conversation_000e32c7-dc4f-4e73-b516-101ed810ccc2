import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/models/supplier.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_details_screen.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_form_screen.dart';

class SuppliersScreen extends StatelessWidget {
  const SuppliersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final SupplierProvider supplierProvider =
        Provider.of<SupplierProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Suppliers'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (BuildContext context) =>
                      const CreateEditSupplierScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: supplierProvider.suppliers.length,
        itemBuilder: (BuildContext context, int index) {
          final Supplier supplier = supplierProvider.suppliers[index];
          return Card(
            child: ListTile(
              title: Text(supplier.name ?? 'مورد غير محدد'),
              subtitle: Text(supplier.email ?? ''),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              SupplierDetailsScreen(supplier: supplier),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () async {
                      final bool? confirmed = await showDialog<bool>(
                        context: context,
                        builder: (BuildContext context) => AlertDialog(
                          title: const Text('تأكيد الحذف'),
                          content: Text(
                            'هل أنت متأكد من حذف المورد '
                            '"${supplier.name ?? 'غير محدد'}"؟\n'
                            'سيتم حذف جميع البيانات المرتبطة به.',
                          ),
                          actions: <Widget>[
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              child: const Text('إلغاء'),
                            ),
                            TextButton(
                              onPressed: () => Navigator.pop(context, true),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.red,
                              ),
                              child: const Text('حذف'),
                            ),
                          ],
                        ),
                      );

                      if (confirmed == true && context.mounted) {
                        final bool success =
                            await supplierProvider.deleteSupplier(supplier.id!);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                success
                                    ? 'تم حذف المورد بنجاح'
                                    : 'فشل في حذف المورد',
                              ),
                              backgroundColor:
                                  success ? Colors.green : Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
