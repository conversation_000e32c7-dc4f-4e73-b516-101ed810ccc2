import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../config/app_colors.dart';
import '../config/app_design_constants.dart';

/// شبكة الإجراءات السريعة للشاشة الرئيسية
class QuickActionsGrid extends StatelessWidget {
  const QuickActionsGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // العنوان
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'الإجراءات السريعة',
            style: TextStyle(
              fontFamily: 'Tajawal',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),

        // الإجراءات
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: <Widget>[
              _buildQuickActionCard(
                context,
                icon: Icons.point_of_sale,
                title: 'بيع سريع',
                subtitle: 'إنشاء فاتورة بيع جديدة',
                color: AppDesignConstants.primaryColor,
                onTap: () => context.go('/sales/add'),
              ),
              const SizedBox(height: 12),
              _buildQuickActionCard(
                context,
                icon: Icons.shopping_cart,
                title: 'شراء سريع',
                subtitle: 'إنشاء فاتورة شراء جديدة',
                color: AppDesignConstants.accentColor,
                onTap: () => context.go('/purchases/add'),
              ),
              const SizedBox(height: 12),
              _buildQuickActionCard(
                context,
                icon: Icons.inventory,
                title: 'إضافة منتج',
                subtitle: 'إضافة منتج جديد للمخزون',
                color: Colors.green,
                onTap: () => context.go('/products/add'),
              ),
              const SizedBox(height: 12),
              _buildQuickActionCard(
                context,
                icon: Icons.person_add,
                title: 'إضافة عميل',
                subtitle: 'إضافة عميل جديد',
                color: Colors.purple,
                onTap: () => context.go('/customers/add'),
              ),
              const SizedBox(height: 12),
              _buildQuickActionCard(
                context,
                icon: Icons.swap_horiz,
                title: 'التحويل الداخلي',
                subtitle: 'تحويل من المخزن للبقالة',
                color: Colors.blue,
                onTap: () => context.go('/inventory/transfer'),
              ),
              const SizedBox(height: 12),
              _buildQuickActionCard(
                context,
                icon: Icons.inventory_2,
                title: 'جرد البقالة',
                subtitle: 'تعديل كميات البقالة',
                color: Colors.teal,
                onTap: () => context.go('/inventory/adjustment'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بطاقة إجراء سريع حديثة وأنيقة
  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          splashColor: color.withValues(alpha: 0.1),
          highlightColor: color.withValues(alpha: 0.05),
          hoverColor: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: <Widget>[
                // الأيقونة
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 28,
                  ),
                ),

                const SizedBox(width: 20),

                // النص
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        title,
                        style: const TextStyle(
                          fontFamily: 'Tajawal',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontFamily: 'Tajawal',
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // سهم
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: color,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
