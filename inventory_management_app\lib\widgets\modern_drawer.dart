import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../config/app_colors.dart';
import '../config/app_design_constants.dart';
import '../widgets/stats_section.dart';

/// Drawer جانبي حديث وأنيق للإجراءات السريعة
class ModernDrawer extends StatelessWidget {
  const ModernDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: <Widget>[
          // Header أنيق
          _buildHeader(context),

          // الإجراءات السريعة
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: <Widget>[
                // قسم الإحصائيات
                const StatsSection(),

                const Divider(height: 16, thickness: 0.5),

                _buildSectionTitle('إدارة المخزون'),
                _buildQuickAction(
                  context,
                  icon: Icons.swap_horiz,
                  title: 'التحويل الداخلي',
                  subtitle: 'تحويل من المخزن للبقالة',
                  color: Colors.blue,
                  onTap: () =>
                      _navigateAndClose(context, '/inventory/transfer'),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.inventory,
                  title: 'جرد البقالة',
                  subtitle: 'تعديل كميات البقالة',
                  color: Colors.green,
                  onTap: () =>
                      _navigateAndClose(context, '/inventory/adjustment'),
                ),
                const Divider(height: 16, thickness: 0.5),
                _buildSectionTitle('العمليات التجارية'),
                _buildQuickAction(
                  context,
                  icon: Icons.local_shipping,
                  title: 'الموردين',
                  subtitle: 'قائمة الموردين وتفاصيلهم',
                  color: Colors.cyan,
                  onTap: () => _navigateAndClose(context, '/suppliers'),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.assignment,
                  title: 'الطلبات',
                  subtitle: 'تسجيل طلبات المحل',
                  color: Colors.orange,
                  onTap: () => _navigateAndClose(context, '/orders'),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.money_off,
                  title: 'المصروفات',
                  subtitle: 'إدارة المصروفات اليومية',
                  color: Colors.red,
                  onTap: () => _navigateAndClose(context, '/expenses'),
                ),
                const Divider(height: 16, thickness: 0.5),
                _buildSectionTitle('التقارير والإحصائيات'),
                _buildQuickAction(
                  context,
                  icon: Icons.analytics,
                  title: 'التقارير',
                  subtitle: 'عرض التقارير والإحصائيات',
                  color: Colors.indigo,
                  onTap: () => _navigateAndClose(context, '/reports'),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.trending_up,
                  title: 'التحليلات',
                  subtitle: 'تحليل الأداء والمبيعات',
                  color: Colors.teal,
                  onTap: () => _navigateAndClose(context, '/analytics'),
                ),
                const Divider(height: 16, thickness: 0.5),
                _buildSectionTitle('الإدارة'),
                _buildQuickAction(
                  context,
                  icon: Icons.category,
                  title: 'الفئات والوحدات',
                  subtitle: 'إدارة فئات المنتجات والوحدات',
                  color: Colors.purple,
                  onTap: () => _navigateAndClose(context, '/categories'),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.backup,
                  title: 'النسخ الاحتياطي',
                  subtitle: 'إدارة النسخ الاحتياطية',
                  color: Colors.orange,
                  onTap: () => _navigateAndClose(context, '/backup'),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.settings,
                  title: 'الإعدادات',
                  subtitle: 'إعدادات التطبيق',
                  color: Colors.grey,
                  onTap: () => _navigateAndClose(context, '/settings'),
                ),
              ],
            ),
          ),

          // Footer
          _buildFooter(context),
        ],
      ),
    );
  }

  /// Header أنيق مع بطاقة حديثة لمنع الـ overflow
  Widget _buildHeader(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.only(
          top: 24.0,
          left: 16.0,
          right: 16.0,
          bottom: 16.0,
        ),
        child: Card(
          elevation: 4.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          margin: EdgeInsets.zero,
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: <Widget>[
                // أيقونة المتجر
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppDesignConstants.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.storefront,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات المتجر
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      // اسم المتجر
                      Text(
                        'أسامة ماركت',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Tajawal',
                              color: AppDesignConstants.primaryColor,
                            ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),

                      const SizedBox(height: 4),

                      // وصف المتجر
                      Text(
                        'نظام إدارة المخزون الذكي',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontFamily: 'Tajawal',
                              color: AppColors.textSecondary,
                            ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 6),
      child: Text(
        title,
        style: const TextStyle(
          fontFamily: 'Tajawal',
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  /// إجراء سريع أنيق
  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          splashColor: color.withValues(alpha: 0.1),
          highlightColor: color.withValues(alpha: 0.05),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: <Widget>[
                // الأيقونة
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),

                const SizedBox(width: 16),

                // النص
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        title,
                        style: const TextStyle(
                          fontFamily: 'Tajawal',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontFamily: 'Tajawal',
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // سهم
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textTertiary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Footer مع معلومات إضافية
  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        border: Border(
          top: BorderSide(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: <Widget>[
          Icon(
            Icons.info_outline,
            color: AppColors.textTertiary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'الإصدار 1.0.0',
            style: TextStyle(
              fontFamily: 'Tajawal',
              fontSize: 12,
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  /// التنقل وإغلاق الـ drawer
  void _navigateAndClose(BuildContext context, String route) {
    Navigator.of(context).pop(); // إغلاق الـ drawer
    context.go(route); // التنقل للصفحة
  }
}
