/// Model class for OrderItem (عنصر طلبية المحل)
class OrderItem {
  /// Unique identifier for the order item
  int? id;

  /// ID of the order this item belongs to
  int? orderId;

  /// ID of the product
  int? productId;

  /// Quantity of the product needed
  double? quantity;

  /// Estimated price per unit for this order item
  double? price;

  /// Product name for display purposes
  String? productName;

  /// Current stock level when order was created
  double? currentStock;

  /// Minimum stock level that triggered this order item
  double? minStock;

  /// Unit price (alias for price)
  double? get unitPrice => price;

  /// Total estimated price for this item (quantity * unit price)
  double? get totalPrice => (quantity ?? 0) * (price ?? 0);

  /// Constructor for creating an OrderItem instance
  OrderItem({
    this.id,
    this.orderId,
    this.productId,
    this.quantity,
    this.price,
    this.productName,
    this.currentStock,
    this.minStock,
    double? unitPrice,
    double? totalPrice, // This will be calculated automatically
  }) {
    if (unitPrice != null) {
      price = unitPrice;
    }
  }

  /// Converts the OrderItem instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'orderId': orderId,
      'productId': productId,
      'quantity': quantity,
      'price': price,
      'productName': productName,
      'currentStock': currentStock,
      'minStock': minStock,
    };
  }

  /// Creates an OrderItem instance from a Map (typically from database)
  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'] as int?,
      orderId: map['orderId'] as int?,
      productId: map['productId'] as int?,
      quantity: map['quantity']?.toDouble(),
      price: map['price']?.toDouble(),
      productName: map['productName'] as String?,
      currentStock: map['currentStock']?.toDouble(),
      minStock: map['minStock']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'OrderItem{id: $id, orderId: $orderId, productId: $productId, '
        'quantity: $quantity, price: $price, productName: $productName, '
        'currentStock: $currentStock, minStock: $minStock}';
  }

  /// Copy with method for creating modified copies
  OrderItem copyWith({
    int? id,
    int? orderId,
    int? productId,
    String? productName,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    double? currentStock,
    double? minStock,
  }) {
    return OrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? price,
      currentStock: currentStock ?? this.currentStock,
      minStock: minStock ?? this.minStock,
    );
  }
}
