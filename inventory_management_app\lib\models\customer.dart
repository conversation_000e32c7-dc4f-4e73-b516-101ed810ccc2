/// Model class representing a customer
class Customer {
  /// Unique identifier for the customer
  int? id;

  /// Name of the customer
  String name;

  /// Email address of the customer
  String? email;

  /// Phone number of the customer
  String? phone;

  /// Address of the customer
  String? address;

  /// Balance of the customer (debt)
  double? balance;

  /// Creation date of the customer record
  DateTime? createdAt;

  /// Last update date of the customer record
  DateTime? updatedAt;

  /// Notes about the customer
  String? notes;

  /// Constructor for creating a Customer instance
  Customer({
    this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.balance,
    this.createdAt,
    this.updatedAt,
    this.notes,
  });

  /// Converts the Customer instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'balance': balance,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'notes': notes,
    };
  }

  /// Creates a Customer instance from a Map (typically from database)
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'] as int?,
      name: map['name'] as String? ?? '',
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      address: map['address'] as String?,
      balance: map['balance'] as double?,
      createdAt: map['created_at'] != null
          ? DateTime.tryParse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.tryParse(map['updated_at'] as String)
          : null,
      notes: map['notes'] as String?,
    );
  }

  /// Creates a copy of this Customer with the given fields replaced with new values
  Customer copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    double? balance,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'Customer{id: $id, name: $name, email: $email, '
        'phone: $phone, address: $address}';
  }
}
